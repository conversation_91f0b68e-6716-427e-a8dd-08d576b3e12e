<?php
/**
 * 实习盖章模块API接口
 * 提供订单创建、查询、更新等功能
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入必要的文件
require_once('../confing/common.php');
require_once('file_manager.php');

// 添加数据库方法兼容性
if (!method_exists($DB, 'get_var')) {
    // 如果没有get_var方法，添加一个兼容方法
    class DBWrapper {
        private $db;

        public function __construct($db) {
            $this->db = $db;
        }

        public function get_var($query) {
            $result = $this->db->get_row($query);
            if ($result && is_array($result)) {
                $values = array_values($result);
                return $values[0];
            }
            return null;
        }

        public function __get($property) {
            // 处理属性访问，如 insert_id
            if (isset($this->db->$property)) {
                return $this->db->$property;
            }
            return null;
        }

        public function __call($method, $args) {
            if (method_exists($this->db, $method)) {
                return $this->db->$method(...$args);
            }
            return null;
        }
    }

    $DB = new DBWrapper($DB);
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 获取请求参数
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$method = $_SERVER['REQUEST_METHOD'];

// 路由处理
try {
    switch ($action) {
        case 'create_order':
            if ($method === 'POST') {
                createOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'upload_file':
            if ($method === 'POST') {
                uploadFile();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_orders':
            if ($method === 'GET') {
                getOrders();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_order':
            if ($method === 'GET') {
                getOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'update_order':
            if ($method === 'POST') {
                updateOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'admin_orders':
            if ($method === 'GET') {
                getAdminOrders();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'admin_update':
            if ($method === 'POST') {
                adminUpdateOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_statistics':
            if ($method === 'GET') {
                getStatistics();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_admin_stats':
            if ($method === 'GET') {
                getAdminStats();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_user_rates':
            if ($method === 'GET') {
                getUserRates();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_companies':
            if ($method === 'GET') {
                // 检查是否是管理员请求
                if (isset($_GET['admin']) && $_GET['admin'] == '1') {
                    getCompaniesForAdmin();
                } else {
                    getCompanies();
                }
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'add_company':
            if ($method === 'POST') {
                addCompany();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'update_company':
            if ($method === 'POST') {
                updateCompany();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'delete_company':
            if ($method === 'POST') {
                deleteCompany();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'delete_file':
            if ($method === 'POST') {
                deleteFile();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'delete_order':
            if ($method === 'POST') {
                deleteOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'apply_refund':
            if ($method === 'POST') {
                applyRefund();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'process_refund':
            if ($method === 'POST') {
                processRefund();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'update_order_status':
            if ($method === 'POST') {
                updateOrderStatus();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'handle_refund':
            if ($method === 'POST') {
                handleRefund();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'process_failed_order':
            if ($method === 'POST') {
                processFailedOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_refund_requests':
            if ($method === 'GET') {
                getRefundRequests();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'export_orders':
            if ($method === 'GET') {
                exportOrders();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_companies_for_agent':
            if ($method === 'GET') {
                getCompaniesForAgent();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        default:
            throw new Exception('Invalid action', 400);
    }
} catch (Exception $e) {
    http_response_code($e->getCode() ?: 500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'code' => $e->getCode()
    ]);
}

/**
 * 验证用户登录
 */
function validateUser($requireAdmin = false) {
    global $userrow;

    if (!isset($userrow) || !$userrow) {
        throw new Exception('用户未登录', 401);
    }

    if ($requireAdmin && $userrow['uid'] != 1) {
        throw new Exception('权限不足', 403);
    }

    return $userrow;
}

/**
 * 创建订单
 */
function createOrder() {
    global $DB, $userrow;

    try {
        // 验证用户登录
        validateUser();

        // 获取POST数据
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data) {
            $data = $_POST;
        }

        // 过滤和验证输入参数
        $filteredData = [];

        // 必要字段过滤 - 使用 $DB->escape() 提供更强安全保护
        $filteredData['service_type'] = isset($data['service_type']) ? $DB->escape(trim(strip_tags($data['service_type']))) : '';
        $filteredData['company_id'] = isset($data['company_id']) ? intval($data['company_id']) : 0;
        $filteredData['customer_name'] = isset($data['customer_name']) ? $DB->escape(trim(strip_tags($data['customer_name']))) : '';

        // 可选字段过滤 - 使用 $DB->escape() 提供更强安全保护
        $filteredData['customer_email'] = isset($data['customer_email']) ? $DB->escape(trim(strip_tags($data['customer_email']))) : '';
        $filteredData['customer_phone'] = isset($data['customer_phone']) ? $DB->escape(trim(strip_tags($data['customer_phone']))) : '';
        $filteredData['customer_address'] = isset($data['customer_address']) ? $DB->escape(trim(strip_tags($data['customer_address']))) : '';
        $filteredData['courier_company'] = isset($data['courier_company']) ? $DB->escape(trim(strip_tags($data['courier_company']))) : '';
        $filteredData['tracking_number'] = isset($data['tracking_number']) ? $DB->escape(trim(strip_tags($data['tracking_number']))) : '';
        $filteredData['material_type'] = isset($data['material_type']) ? $DB->escape(trim(strip_tags($data['material_type']))) : '';
        $filteredData['special_requirements'] = isset($data['special_requirements']) ? $DB->escape(trim(strip_tags($data['special_requirements']))) : '';

        // 布尔值字段
        $filteredData['business_license'] = !empty($data['business_license']);
        $filteredData['only_business_license'] = !empty($data['only_business_license']);

        // 数值字段
        $filteredData['print_copies'] = isset($data['print_copies']) ? intval($data['print_copies']) : 0;

        // 数组字段过滤
        $filteredData['print_options'] = isset($data['print_options']) && is_array($data['print_options']) ? $data['print_options'] : [];
        $filteredData['license_company_details'] = isset($data['license_company_details']) && is_array($data['license_company_details']) ? $data['license_company_details'] : [];

        // 使用过滤后的数据
        $data = $filteredData;

        // 验证必要字段
        $required = ['service_type', 'company_id', 'customer_name'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new Exception("缺少必要字段: {$field}", 400);
            }
        }

    // 生成订单号
    $orderNo = 'SXGZ' . date('YmdHis') . rand(1000, 9999);

    // 获取实习盖章分类ID
    $categoryRow = $DB->get_row("SELECT id FROM qingka_wangke_fenlei WHERE name = '实习盖章' LIMIT 1");
    if (!$categoryRow) {
        throw new Exception('实习盖章分类不存在', 400);
    }

    $categoryId = $categoryRow['id'];

    // 获取公司信息
    $company = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid = '{$data['company_id']}' AND fenlei = '{$categoryId}' AND status = 1");
    if (!$company) {
        throw new Exception('无效的公司ID或公司已下架', 400);
    }

    // 计算价格
    $pricing = calculatePricing($data, $company);

    // 准备营业执照公司信息
    $licenseCompanyInfo = '';
    if (!empty($data['license_company_details']) && is_array($data['license_company_details'])) {
        $licenseCompanyInfo = json_encode($data['license_company_details'], JSON_UNESCAPED_UNICODE);
    }

    // 准备插入数据（数据已经在前面用 $DB->escape() 处理过了，这里不需要重复转义）
    $insertData = [
        'uid' => intval($userrow['uid']),
        'order_no' => $DB->escape($orderNo),
        'service_type' => $data['service_type'], // 已经过滤过了
        'company_id' => intval($data['company_id']),
        'company_name' => $DB->escape($company['name']),
        'business_license' => !empty($data['business_license']) ? 1 : 0,
        'only_business_license' => !empty($data['only_business_license']) ? 1 : 0,
        'material_type' => !empty($data['material_type']) ? $data['material_type'] : null, // 已经过滤过了
        'customer_name' => $data['customer_name'], // 已经过滤过了
        'customer_email' => !empty($data['customer_email']) ? $data['customer_email'] : null, // 已经过滤过了
        'customer_phone' => !empty($data['customer_phone']) ? $data['customer_phone'] : null, // 已经过滤过了
        'customer_address' => !empty($data['customer_address']) ? $data['customer_address'] : null, // 已经过滤过了
        'courier_company' => !empty($data['courier_company']) ? $data['courier_company'] : null, // 已经过滤过了
        'tracking_number' => !empty($data['tracking_number']) ? $data['tracking_number'] : null, // 已经过滤过了
        'print_copies' => intval($data['print_copies'] ?? 0),
        'print_options' => isset($data['print_options']) ? $DB->escape(json_encode($data['print_options'], JSON_UNESCAPED_UNICODE)) : null,
        'special_requirements' => !empty($data['special_requirements']) ? $data['special_requirements'] : null, // 已经过滤过了
        'base_price' => floatval($pricing['base_price']),
        'print_price' => floatval($pricing['print_price']),
        'license_price' => floatval($pricing['license_price']),
        'total_price' => floatval($pricing['total_price']),
        'status' => 'pending',
        'source' => 'direct', // 标记订单来源为直接下单
        'agent_uid' => null, // 直接下单无代理商
        'created_at' => date('Y-m-d H:i:s')
    ];

    // 如果有营业执照公司信息，添加到special_requirements中
    if (!empty($licenseCompanyInfo)) {
        $existingRequirements = $insertData['special_requirements'] ?? '';

        // 构建详细的营业执照公司信息
        $licenseDetails = [];
        foreach ($data['license_company_details'] as $company) {
            $licenseDetails[] = $company['name'] . '(¥' . $company['price'] . ')';
        }
        $licenseInfo = "营业执照公司: " . implode(', ', $licenseDetails);

        if (!empty($existingRequirements)) {
            $insertData['special_requirements'] = $DB->escape($existingRequirements . ' | ' . $licenseInfo);
        } else {
            $insertData['special_requirements'] = $DB->escape($licenseInfo);
        }
    }

    // 插入订单 - 使用项目常规方法
    $fields = array_keys($insertData);
    $values = array_values($insertData);

    // 转义字符串值
    $escapedValues = array_map(function($value) use ($DB) {
        if (is_string($value)) {
            return "'" . $DB->escape($value) . "'";
        } elseif (is_null($value)) {
            return 'NULL';
        } else {
            return $value;
        }
    }, $values);

    $sql = "INSERT INTO FD_sxgz_orders (`" . implode('`, `', $fields) . "`) VALUES (" . implode(', ', $escapedValues) . ")";

    $result = $DB->query($sql);
    if (!$result) {
        throw new Exception('订单创建失败: ' . $DB->error(), 500);
    }

    // 获取插入的订单ID - 通过查询获取
    $orderRecord = $DB->get_row("SELECT order_id FROM FD_sxgz_orders WHERE order_no = '{$insertData['order_no']}' LIMIT 1");
    $orderId = $orderRecord ? intval($orderRecord['order_id']) : 0;

    if (!$orderId) {
        throw new Exception('订单创建成功但无法获取订单ID', 500);
    }

    // 扣除用户余额
    $DB->query("UPDATE qingka_wangke_user SET money = money - {$pricing['total_price']} WHERE uid = {$userrow['uid']}");

    // 记录日志
    wlog($userrow['uid'], '实习盖章下单', "订单号: {$orderNo}, 金额: {$pricing['total_price']}元", -$pricing['total_price']);

    // 添加到邮件队列
    if (!empty($data['customer_email'])) {
        addToEmailQueue($orderId, 'order_confirmation', $data['customer_email'], $data['customer_name']);
    }

        echo json_encode([
            'success' => true,
            'message' => '订单创建成功',
            'data' => [
                'order_id' => $orderId,
                'order_no' => $orderNo,
                'total_price' => $pricing['total_price']
            ]
        ]);

    } catch (Exception $e) {
        error_log('CreateOrder error: ' . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'error_code' => $e->getCode()
        ]);
    }
}

/**
 * 文件上传
 */
function uploadFile() {
    global $DB, $userrow;

    // 验证用户登录
    if (!isset($userrow) || !$userrow) {
        echo json_encode([
            'code' => 0,
            'msg' => '用户未登录'
        ]);
        return;
    }

    if (!isset($_FILES['file'])) {
        echo json_encode([
            'code' => 0,
            'msg' => '没有上传文件'
        ]);
        return;
    }

    $file = $_FILES['file'];
    $orderId = $_POST['order_id'] ?? '';
    $isReplacement = isset($_POST['is_replacement']) && $_POST['is_replacement'] === 'true';

    // 基本验证
    if ($file['error'] !== UPLOAD_ERR_OK) {
        echo json_encode([
            'code' => 0,
            'msg' => '文件上传错误: ' . $file['error']
        ]);
        return;
    }

    // 文件类型验证
    $allowedTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'zip', 'rar', '7z'];
    $fileExt = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($fileExt, $allowedTypes)) {
        echo json_encode([
            'code' => 0,
            'msg' => '不支持的文件类型'
        ]);
        return;
    }

    // 文件大小验证（10MB）
    if ($file['size'] > 10 * 1024 * 1024) {
        echo json_encode([
            'code' => 0,
            'msg' => '文件大小不能超过 10MB'
        ]);
        return;
    }

    // 如果提供了订单ID，验证订单
    if (!empty($orderId)) {
        $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}' AND uid = {$userrow['uid']}");
        if (!$order) {
            echo json_encode([
                'code' => 0,
                'msg' => '订单不存在或无权限'
            ]);
            return;
        }

        // 有订单ID的情况 - 使用正式目录结构
        $uploadDir = __DIR__ . '/uploads/uid_' . $userrow['uid'] . '/orderid_' . $orderId . '/';
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                echo json_encode([
                    'code' => 0,
                    'msg' => '创建上传目录失败'
                ]);
                return;
            }
        }

        $newFileName = date('YmdHis') . '_' . uniqid() . '.' . $fileExt;
        $uploadPath = $uploadDir . $newFileName;

        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            // 更新数据库
            $downloadUrl = '/sxgz/download.php?uid=' . $userrow['uid'] . '&order_id=' . $orderId . '&file=' . $newFileName;
            $originalName = $DB->escape($file['name']);
            $fileSize = intval($file['size']);

            $updateSql = "UPDATE FD_sxgz_orders SET
                            uploaded_file = '{$downloadUrl}',
                            original_filename = '{$originalName}',
                            file_size = {$fileSize},
                            updated_at = NOW()
                          WHERE order_id = '{$orderId}'";

            $DB->query($updateSql);

            echo json_encode([
                'code' => 1,
                'msg' => $isReplacement ? '文件替换成功' : '文件上传成功',
                'downurl' => $downloadUrl,
                'data' => [
                    'filename' => $newFileName,
                    'original_name' => $file['name'],
                    'size' => $file['size'],
                    'download_url' => $downloadUrl
                ]
            ]);
        } else {
            echo json_encode([
                'code' => 0,
                'msg' => '文件保存失败'
            ]);
        }
    } else {
        // 临时上传（无订单关联）
        $allowedExts = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'zip', 'rar', '7z'];
        $fileExt = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

        if (!in_array($fileExt, $allowedExts)) {
            echo json_encode([
                'code' => 0,
                'msg' => '不支持的文件类型，只支持：' . implode(', ', $allowedExts)
            ]);
            return;
        }

        if ($file['size'] > 10 * 1024 * 1024) {
            echo json_encode([
                'code' => 0,
                'msg' => '文件大小不能超过10MB'
            ]);
            return;
        }

        // 临时文件存储
        $uploadDir = __DIR__ . '/uploads/temp/';

        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                echo json_encode([
                    'code' => 0,
                    'msg' => '创建上传目录失败'
                ]);
                return;
            }
        }

        $newFileName = date('YmdHis') . '_' . uniqid() . '.' . $fileExt;
        $uploadPath = $uploadDir . $newFileName;

        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            echo json_encode([
                'code' => 1,
                'msg' => '文件上传成功',
                'downurl' => '/sxgz/uploads/temp/' . $newFileName,
                'data' => [
                    'filename' => $newFileName,
                    'original_name' => $file['name'],
                    'size' => $file['size']
                ]
            ]);
        } else {
            echo json_encode([
                'code' => 0,
                'msg' => '保存文件失败'
            ]);
        }
    }
}

/**
 * 获取订单列表
 */
function getOrders() {
    global $DB, $userrow;

    validateUser();

    // 检查表是否存在
    $tableExistsResult = $DB->get_row("SHOW TABLES LIKE 'FD_sxgz_orders'");
    $tableExists = !empty($tableExistsResult);
    error_log("表存在检查 - FD_sxgz_orders: " . ($tableExists ? '存在' : '不存在'));

    if (!$tableExists) {
        echo json_encode([
            'success' => false,
            'message' => '订单表不存在',
            'data' => [
                'orders' => [],
                'total' => 0,
                'page' => 1,
                'pages' => 0
            ]
        ]);
        return;
    }

    // 过滤输入参数
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 15);
    $offset = ($page - 1) * $limit;

    // 管理员可以查看所有订单，普通用户只能查看自己的订单
    if ($userrow['uid'] == 1) {
        $where = "WHERE 1=1"; // 管理员查看所有订单
        $tablePrefix = "o."; // 管理员查询使用表别名
    } else {
        $where = "WHERE uid = {$userrow['uid']}"; // 普通用户只查看自己的订单
        $tablePrefix = ""; // 普通用户查询不使用表别名
    }

    // 搜索条件
    if (!empty($_GET['search'])) {
        $search = $DB->escape(trim(strip_tags($_GET['search'])));
        $searchField = isset($_GET['search_field']) ? $DB->escape(trim(strip_tags($_GET['search_field']))) : '';

        // 调试信息（可以在生产环境中删除）
        error_log("搜索参数 - search: {$search}, search_field: {$searchField}");

        if (!empty($searchField)) {
            // 根据指定字段搜索
            switch ($searchField) {
                case 'uid':
                    if ($userrow['uid'] === 1) { // 只有管理员可以按UID搜索
                        $where .= " AND {$tablePrefix}uid = '{$search}'";
                    }
                    break;
                case 'username':
                    if ($userrow['uid'] === 1) { // 只有管理员可以按用户名搜索
                        // 由于管理员查询时已经JOIN了用户表，可以直接搜索
                        $where .= " AND (u.user LIKE '%{$search}%' OR u.name LIKE '%{$search}%')";
                    }
                    break;
                case 'oid':
                    if ($userrow['uid'] === 1) { // 只有管理员可以按订单ID搜索
                        $where .= " AND {$tablePrefix}order_id LIKE '%{$search}%'";
                    }
                    break;
                case 'customer_name':
                    $where .= " AND {$tablePrefix}customer_name LIKE '%{$search}%'";
                    break;
                case 'company_name':
                    $where .= " AND {$tablePrefix}company_name LIKE '%{$search}%'";
                    break;
                case 'order_no':
                    $where .= " AND {$tablePrefix}order_no LIKE '%{$search}%'";
                    break;
                case 'special_requirements':
                    $where .= " AND {$tablePrefix}special_requirements LIKE '%{$search}%'";
                    break;
                case 'admin_notes':
                    $where .= " AND {$tablePrefix}admin_notes LIKE '%{$search}%'";
                    break;
                default:
                    // 默认全字段搜索 - 包含所有可搜索字段
                    if ($userrow['uid'] === 1) {
                        // 管理员可以搜索用户信息
                        $where .= " AND ({$tablePrefix}order_id LIKE '%{$search}%' OR {$tablePrefix}order_no LIKE '%{$search}%' OR {$tablePrefix}customer_name LIKE '%{$search}%' OR {$tablePrefix}company_name LIKE '%{$search}%' OR {$tablePrefix}special_requirements LIKE '%{$search}%' OR {$tablePrefix}admin_notes LIKE '%{$search}%' OR u.user LIKE '%{$search}%' OR u.name LIKE '%{$search}%')";
                    } else {
                        $where .= " AND ({$tablePrefix}order_id LIKE '%{$search}%' OR {$tablePrefix}order_no LIKE '%{$search}%' OR {$tablePrefix}customer_name LIKE '%{$search}%' OR {$tablePrefix}company_name LIKE '%{$search}%' OR {$tablePrefix}special_requirements LIKE '%{$search}%' OR {$tablePrefix}admin_notes LIKE '%{$search}%')";
                    }
                    break;
            }
        } else {
            // 全字段搜索 - 包含所有可搜索字段
            if ($userrow['uid'] === 1) {
                // 管理员可以搜索用户信息
                $where .= " AND ({$tablePrefix}order_id LIKE '%{$search}%' OR {$tablePrefix}order_no LIKE '%{$search}%' OR {$tablePrefix}customer_name LIKE '%{$search}%' OR {$tablePrefix}company_name LIKE '%{$search}%' OR {$tablePrefix}special_requirements LIKE '%{$search}%' OR {$tablePrefix}admin_notes LIKE '%{$search}%' OR u.user LIKE '%{$search}%' OR u.name LIKE '%{$search}%')";
            } else {
                $where .= " AND ({$tablePrefix}order_id LIKE '%{$search}%' OR {$tablePrefix}order_no LIKE '%{$search}%' OR {$tablePrefix}customer_name LIKE '%{$search}%' OR {$tablePrefix}company_name LIKE '%{$search}%' OR {$tablePrefix}special_requirements LIKE '%{$search}%' OR {$tablePrefix}admin_notes LIKE '%{$search}%')";
            }
        }
    }

    if (!empty($_GET['status'])) {
        $status = $DB->escape(trim(strip_tags($_GET['status'])));
        $where .= " AND {$tablePrefix}status = '{$status}'";
    }

    // 调试信息 - 输出最终的WHERE条件
    error_log("最终WHERE条件: {$where}");

    // 根据用户权限构建不同的查询
    if ($userrow['uid'] == 1) {
        // 管理员查询 - 包含用户信息
        $countSql = "SELECT COUNT(*) FROM FD_sxgz_orders o {$where}";
        $total = $DB->count($countSql);

        $orderSql = "SELECT o.*, u.user as username, u.name as user_realname
                     FROM FD_sxgz_orders o
                     LEFT JOIN qingka_wangke_user u ON o.uid = u.uid
                     {$where} ORDER BY
                     CASE
                         WHEN o.status = 'processing' THEN 1
                         WHEN o.status = 'refund_requested' THEN 2
                         WHEN o.status = 'pending' THEN 3
                         ELSE 4
                     END ASC,
                     o.created_at DESC
                     LIMIT {$offset}, {$limit}";
    } else {
        // 普通用户查询 - 不包含用户信息
        $countSql = "SELECT COUNT(*) FROM FD_sxgz_orders {$where}";
        $total = $DB->count($countSql);

        $orderSql = "SELECT * FROM FD_sxgz_orders {$where} ORDER BY
                     CASE
                         WHEN status = 'processing' THEN 1
                         WHEN status = 'refund_requested' THEN 2
                         WHEN status = 'pending' THEN 3
                         ELSE 4
                     END ASC,
                     created_at DESC
                     LIMIT {$offset}, {$limit}";
    }

    error_log("计数SQL: {$countSql}");
    error_log("查询SQL: {$orderSql}");
    $orders = $DB->get_results($orderSql);

    // 调试信息 - 输出查询结果
    error_log("查询结果 - 总数: {$total}, 当前页订单数: " . count($orders));

    echo json_encode([
        'success' => true,
        'data' => [
            'orders' => $orders,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

/**
 * 获取单个订单详情
 */
function getOrder() {
    global $DB, $userrow;

    // 初始化文件管理器
    require_once('file_manager.php');
    $fileManager = new SxgzFileManager();

    $orderId = $_GET['order_id'] ?? '';
    if (empty($orderId)) {
        throw new Exception('缺少订单ID', 400);
    }

    $where = "WHERE order_id = '{$orderId}'";

    // 非管理员只能查看自己的订单
    if (!isset($userrow) || $userrow['uid'] != 1) {
        if (!isset($userrow) || !$userrow) {
            throw new Exception('用户未登录', 401);
        }
        $where .= " AND uid = {$userrow['uid']}";
    }

    $order = $DB->get_row("SELECT * FROM FD_sxgz_orders {$where}");

    if (!$order) {
        throw new Exception('订单不存在', 404);
    }

    // 获取文件列表
    $files = $fileManager->getOrderFiles($orderId);

    echo json_encode([
        'success' => true,
        'data' => [
            'order' => $order,
            'files' => $files
        ]
    ]);
}

/**
 * 更新订单
 */
function updateOrder() {
    global $DB, $userrow;

    validateUser();

    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data) {
        $data = $_POST;
    }

    // 过滤输入参数
    $orderId = isset($data['order_id']) ? $DB->escape(trim(strip_tags($data['order_id']))) : '';
    if (empty($orderId)) {
        throw new Exception('缺少订单ID', 400);
    }

    // 验证订单存在且属于当前用户
    $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}' AND uid = {$userrow['uid']}");
    if (!$order) {
        throw new Exception('订单不存在', 404);
    }

    // 只允许更新pending状态的订单
    if ($order['status'] !== 'pending') {
        throw new Exception('订单状态不允许修改', 400);
    }

    // 准备更新数据
    $updateFields = [];
    $allowedFields = ['customer_name', 'customer_email', 'customer_phone', 'customer_address', 'special_requirements'];

    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            // 过滤输入值 - 使用 $DB->escape() 提供更强安全保护
            $value = $DB->escape(trim(strip_tags($data[$field])));
            $updateFields[] = "{$field} = '{$value}'";
        }
    }

    if (empty($updateFields)) {
        throw new Exception('没有可更新的字段', 400);
    }

    $updateFields[] = "updated_at = NOW()";
    $updateSql = "UPDATE FD_sxgz_orders SET " . implode(', ', $updateFields) . " WHERE order_id = '{$orderId}'";

    if ($DB->query($updateSql)) {
        echo json_encode([
            'success' => true,
            'message' => '订单更新成功'
        ]);
    } else {
        throw new Exception('订单更新失败', 500);
    }
}

/**
 * 获取公司列表（用户端）
 */
function getCompanies() {
    global $DB;

    try {
        // 获取实习盖章分类ID
        $categoryRow = $DB->get_row("SELECT id FROM qingka_wangke_fenlei WHERE name = '实习盖章' LIMIT 1");
        if (!$categoryRow) {
            echo json_encode([
                'success' => false,
                'message' => '实习盖章分类不存在'
            ]);
            return;
        }

        $categoryId = $categoryRow['id'];

        // 获取实习盖章分类的公司
        $companies = $DB->get_results("SELECT cid, name, price, content, status, fenlei, addtime FROM qingka_wangke_class WHERE fenlei = '{$categoryId}' AND status = 1 ORDER BY sort ASC, name ASC");

        // 转换为数组格式
        $companiesArray = [];
        if ($companies) {
            foreach ($companies as $company) {
                $companiesArray[] = (array)$company;
            }
        }

        echo json_encode([
            'success' => true,
            'data' => $companiesArray
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '获取公司列表失败: ' . $e->getMessage()
        ]);
    }
}

/**
 * 管理员获取订单列表
 */
function getAdminOrders() {
    global $DB, $userrow;

    // 验证管理员权限
    validateUser(true);

    // 过滤输入参数
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 15);
    $offset = ($page - 1) * $limit;

    $where = "WHERE 1=1";

    // 搜索条件
    if (!empty($_GET['search'])) {
        $search = $DB->escape(trim(strip_tags($_GET['search'])));
        $searchField = isset($_GET['search_field']) ? $DB->escape(trim(strip_tags($_GET['search_field']))) : '';

        switch ($searchField) {
            case 'order_no':
                $where .= " AND order_no LIKE '%{$search}%'";
                break;
            case 'customer_name':
                $where .= " AND customer_name LIKE '%{$search}%'";
                break;
            case 'customer_email':
                $where .= " AND customer_email LIKE '%{$search}%'";
                break;
            default:
                // 管理员全字段搜索 - 包含所有可搜索字段
                $where .= " AND (order_id LIKE '%{$search}%' OR order_no LIKE '%{$search}%' OR customer_name LIKE '%{$search}%' OR customer_email LIKE '%{$search}%' OR company_name LIKE '%{$search}%')";
        }
    }

    if (!empty($_GET['status'])) {
        $status = $DB->escape(trim(strip_tags($_GET['status'])));
        $where .= " AND status = '{$status}'";
    }

    if (!empty($_GET['service_type'])) {
        $serviceType = $DB->escape(trim(strip_tags($_GET['service_type'])));
        $where .= " AND service_type = '{$serviceType}'";
    }

    if (!empty($_GET['date_from'])) {
        $dateFrom = $DB->escape($_GET['date_from']);
        $where .= " AND DATE(created_at) >= '{$dateFrom}'";
    }

    if (!empty($_GET['date_to'])) {
        $dateTo = $DB->escape($_GET['date_to']);
        $where .= " AND DATE(created_at) <= '{$dateTo}'";
    }

    // 获取总数
    $total = $DB->count("SELECT COUNT(*) FROM FD_sxgz_orders {$where}");

    // 获取订单列表 - 处理中>申请退款>待处理的订单顶置
    $orders = $DB->get_results("SELECT * FROM FD_sxgz_orders {$where} ORDER BY
        CASE
            WHEN status = 'processing' THEN 1
            WHEN status = 'refund_requested' THEN 2
            WHEN status = 'pending' THEN 3
            ELSE 4
        END ASC,
        created_at DESC
        LIMIT {$offset}, {$limit}");

    echo json_encode([
        'success' => true,
        'data' => [
            'orders' => $orders,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

/**
 * 管理员更新订单
 */
function adminUpdateOrder() {
    global $DB, $userrow;

    // 验证管理员权限
    validateUser(true);

    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data) {
        $data = $_POST;
    }

    // 过滤输入参数
    $orderId = isset($data['order_id']) ? $DB->escape(trim(strip_tags($data['order_id']))) : '';
    if (empty($orderId)) {
        throw new Exception('缺少订单ID', 400);
    }

    // 验证订单存在
    $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");
    if (!$order) {
        throw new Exception('订单不存在', 404);
    }

    $oldStatus = $order['status'];
    $newStatus = isset($data['status']) ? $DB->escape(trim(strip_tags($data['status']))) : $oldStatus;
    $adminNotes = isset($data['admin_notes']) ? $DB->escape(trim(strip_tags($data['admin_notes']))) : '';

    // 准备更新数据
    $updateFields = [];

    if ($newStatus !== $oldStatus) {
        $updateFields[] = "status = '{$newStatus}'";

        if ($newStatus === 'completed') {
            $updateFields[] = "completed_at = NOW()";

            // 添加到邮件队列
            if (!empty($order['customer_email'])) {
                addToEmailQueue($orderId, 'completion_notice', $order['customer_email'], $order['customer_name']);
            }
        }
    }

    if (!empty($adminNotes)) {
        // $adminNotes 已经在前面用 $DB->escape() 处理过了，这里不需要重复转义
        $updateFields[] = "admin_notes = '{$adminNotes}'";
    }

    if (isset($data['processed_files'])) {
        // 过滤处理文件数据（确保是数组）
        $processedFiles = is_array($data['processed_files']) ? $data['processed_files'] : [];
        $processedFiles = json_encode($processedFiles);
        $updateFields[] = "processed_files = '{$processedFiles}'";
    }

    $updateFields[] = "updated_at = NOW()";

    // 更新订单
    $updateSql = "UPDATE FD_sxgz_orders SET " . implode(', ', $updateFields) . " WHERE order_id = '{$orderId}'";

    if ($DB->query($updateSql)) {
        echo json_encode([
            'success' => true,
            'message' => '订单更新成功'
        ]);
    } else {
        throw new Exception('订单更新失败', 500);
    }
}

/**
 * 计算订单价格
 */
function calculatePricing($data, $company) {
    global $userrow, $DB;

    // 获取用户费率
    $userRate = floatval($userrow['addprice'] ?? 1.0);
    if ($userRate <= 0) {
        $userRate = 1.0; // 默认费率
    }

    // 基础价格 = 公司价格 × 用户费率
    $basePrice = floatval($company['price']) * $userRate;
    $printPrice = 0;
    $licensePrice = 0;

    // 计算打印费用（超过10张每张0.5元，不受用户费率影响）
    $printCopies = intval($data['print_copies'] ?? 0);
    if ($printCopies > 10) {
        $printPrice = ($printCopies - 10) * 0.5;
    }

    // 计算营业执照费用（支持多个营业执照公司）
    if (!empty($data['business_license']) && empty($data['only_business_license'])) {
        // 获取选中的营业执照公司列表
        $selectedLicenseCompanies = $data['selected_license_companies'] ?? [];

        if (!empty($selectedLicenseCompanies) && is_array($selectedLicenseCompanies)) {
            $licensePrice = 0;
            foreach ($selectedLicenseCompanies as $licenseCid) {
                $licenseCompany = $DB->get_row("SELECT price FROM qingka_wangke_class WHERE cid = " . intval($licenseCid));
                if ($licenseCompany) {
                    $licensePrice += floatval($licenseCompany['price']) * $userRate;
                }
            }
        } else {
            // 兼容旧版本：如果没有选择具体公司，使用默认价格
            $licensePrice = 100 * $userRate;
        }
    }

    // 如果仅需要营业执照，使用营业执照专用价格（受用户费率影响）
    if (!empty($data['only_business_license'])) {
        $selectedLicenseCompanies = $data['selected_license_companies'] ?? [];

        if (!empty($selectedLicenseCompanies) && is_array($selectedLicenseCompanies)) {
            $basePrice = 0;
            foreach ($selectedLicenseCompanies as $licenseCid) {
                $licenseCompany = $DB->get_row("SELECT price FROM qingka_wangke_class WHERE cid = " . intval($licenseCid));
                if ($licenseCompany) {
                    $basePrice += floatval($licenseCompany['price']) * $userRate;
                }
            }
        } else {
            // 兼容旧版本：使用默认价格
            $basePrice = 100 * $userRate;
        }
        $licensePrice = 0;
        $printPrice = 0;
    }

    $totalPrice = $basePrice + $printPrice + $licensePrice;

    return [
        'base_price' => $basePrice,
        'print_price' => $printPrice,
        'license_price' => $licensePrice,
        'total_price' => $totalPrice,
        'user_rate' => $userRate
    ];
}

/**
 * 删除文件（管理员功能）
 */
function deleteFile() {
    global $DB, $userrow;

    // 初始化文件管理器
    require_once('file_manager.php');
    $fileManager = new SxgzFileManager();

    // 验证管理员权限
    validateUser(true);

    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data) {
        $data = $_POST;
    }

    $orderId = $data['order_id'] ?? '';
    $filename = $data['filename'] ?? '';
    $fileType = $data['file_type'] ?? 'upload'; // upload 或 processed

    if (empty($orderId) || empty($filename)) {
        throw new Exception('缺少必要参数', 400);
    }

    // 验证订单存在
    $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");
    if (!$order) {
        throw new Exception('订单不存在', 404);
    }

    $uid = $order['uid'];

    // 删除文件
    $result = $fileManager->deleteFile($uid, $orderId, $filename, $fileType);

    if ($result) {
        // 记录日志
        wlog($userrow['uid'], '删除文件', "订单: {$orderId}, 文件: {$filename}, 类型: {$fileType}", 0);

        echo json_encode([
            'success' => true,
            'message' => '文件删除成功'
        ]);
    } else {
        throw new Exception('文件删除失败', 500);
    }
}

/**
 * 用户申请退款
 */
function applyRefund() {
    global $DB, $userrow;

    // 验证用户登录
    validateUser();

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        // 过滤输入参数
        $orderId = isset($input['order_id']) ? $DB->escape(trim(strip_tags($input['order_id']))) : '';
        $reason = isset($input['reason']) ? $DB->escape(trim(strip_tags($input['reason']))) : '';

        if (empty($orderId)) {
            throw new Exception('订单ID不能为空');
        }

        if (empty($reason)) {
            throw new Exception('退款原因不能为空');
        }

        // 验证订单
        $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}' AND uid = {$userrow['uid']}");
        if (!$order) {
            throw new Exception('订单不存在或无权限');
        }

        // 检查订单状态
        if (in_array($order['status'], ['cancelled', 'refunded', 'refund_requested'])) {
            $statusText = [
                'cancelled' => '已取消',
                'refunded' => '已退款',
                'refund_requested' => '已申请退款'
            ];
            throw new Exception('订单状态为' . $statusText[$order['status']] . '，无法申请退款');
        }

        // 更新订单状态和退款原因
        $result = $DB->query("UPDATE FD_sxgz_orders SET
                              status = 'refund_requested',
                              refund_reason = '{$DB->escape($reason)}',
                              updated_at = NOW()
                              WHERE order_id = '{$orderId}'");

        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => '退款申请已提交，请等待管理员审核'
            ]);
        } else {
            throw new Exception('提交退款申请失败');
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * 管理员处理退款申请
 */
function processRefund() {
    global $DB, $userrow;

    // 验证管理员权限
    validateUser(true);

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        // 过滤输入参数
        $orderId = isset($input['order_id']) ? $DB->escape(trim(strip_tags($input['order_id']))) : '';
        $action = isset($input['action']) ? $DB->escape(trim(strip_tags($input['action']))) : ''; // approve 或 reject
        $adminNotes = isset($input['admin_notes']) ? $DB->escape(trim(strip_tags($input['admin_notes']))) : '';

        if (empty($orderId)) {
            throw new Exception('订单ID不能为空');
        }

        if (!in_array($action, ['approve', 'reject'])) {
            throw new Exception('无效的操作');
        }

        // 获取订单信息
        $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");
        if (!$order) {
            throw new Exception('订单不存在');
        }

        if ($order['status'] !== 'refund_requested') {
            throw new Exception('订单未申请退款或已处理');
        }

        if ($action === 'approve') {
            // 批准退款
            $DB->query("UPDATE FD_sxgz_orders SET
                        status = 'refunded',
                        admin_notes = '{$DB->escape($adminNotes)}',
                        updated_at = NOW()
                        WHERE order_id = '{$orderId}'");

            // 退还用户余额
            $DB->query("UPDATE qingka_wangke_user SET money = money + {$order['total_price']} WHERE uid = {$order['uid']}");

            // 记录用户资金日志
            wlog($order['uid'], '实习盖章退款', "订单 {$orderId} 退款成功", $order['total_price']);

            $message = '退款申请已批准，金额已退还到用户余额';

        } else {
            // 拒绝退款，恢复到之前状态
            $previousStatus = 'pending'; // 默认恢复到待处理状态

            $DB->query("UPDATE FD_sxgz_orders SET
                        status = '{$previousStatus}',
                        admin_notes = '{$DB->escape($adminNotes)}',
                        updated_at = NOW()
                        WHERE order_id = '{$orderId}'");

            $message = '退款申请已拒绝，订单恢复处理';
        }

        echo json_encode([
            'success' => true,
            'message' => $message
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * 管理员处理失败订单并自动退款
 */
function processFailedOrder() {
    global $DB, $userrow;

    // 验证管理员权限
    validateUser(true);

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        // 过滤输入参数
        $orderId = isset($input['order_id']) ? $DB->escape(trim(strip_tags($input['order_id']))) : '';

        if (empty($orderId)) {
            throw new Exception('订单ID不能为空');
        }

        // 获取订单信息
        $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");
        if (!$order) {
            throw new Exception('订单不存在');
        }

        // 检查订单状态，只有处理中的订单才能标记为失败
        if ($order['status'] !== 'processing') {
            throw new Exception('只有处理中的订单才能标记为失败');
        }

        // 开始事务
        $DB->query("START TRANSACTION");

        try {
            // 更新订单状态为失败
            $adminNotes = '异常订单无法处理已退款！';
            $result = $DB->query("UPDATE FD_sxgz_orders SET
                                  status = 'failed',
                                  admin_notes = '{$DB->escape($adminNotes)}',
                                  updated_at = NOW()
                                  WHERE order_id = '{$orderId}'");

            if (!$result) {
                throw new Exception('更新订单状态失败');
            }

            // 退还用户余额
            $refundResult = $DB->query("UPDATE qingka_wangke_user SET money = money + {$order['total_price']} WHERE uid = {$order['uid']}");
            if (!$refundResult) {
                throw new Exception('退款失败');
            }

            // 记录用户资金日志
            wlog($order['uid'], '实习盖章处理失败退款', "订单 {$orderId} 处理失败自动退款", $order['total_price']);

            // 提交事务
            $DB->query("COMMIT");

            echo json_encode([
                'success' => true,
                'message' => '订单已标记为处理失败，退款已完成'
            ]);

        } catch (Exception $e) {
            // 回滚事务
            $DB->query("ROLLBACK");
            throw $e;
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * 获取退款申请列表（管理员）
 */
function getRefundRequests() {
    global $DB, $userrow;

    // 验证管理员权限
    validateUser(true);

    try {
        // 过滤输入参数
        $page = intval($_GET['page'] ?? 1);
        $limit = intval($_GET['limit'] ?? 20);
        $status = isset($_GET['status']) ? $DB->escape(trim(strip_tags($_GET['status']))) : '';

        $offset = ($page - 1) * $limit;
        $where = "WHERE o.status IN ('refund_requested', 'refunded')";

        if (!empty($status)) {
            if ($status === 'pending') {
                $where = "WHERE o.status = 'refund_requested'";
            } elseif ($status === 'approved') {
                $where = "WHERE o.status = 'refunded'";
            }
        }

        // 获取总数
        $total = $DB->get_var("
            SELECT COUNT(*)
            FROM FD_sxgz_orders o
            {$where}
        ");

        // 获取退款申请列表
        $refunds = $DB->get_results("
            SELECT o.order_id, o.order_no, o.uid, o.customer_name, o.customer_email,
                   o.company_name, o.service_type, o.total_price as refund_amount,
                   o.refund_reason as reason, o.status, o.admin_notes,
                   o.created_at, o.updated_at, u.user as username
            FROM FD_sxgz_orders o
            LEFT JOIN qingka_wangke_user u ON o.uid = u.uid
            {$where}
            ORDER BY o.updated_at DESC
            LIMIT {$offset}, {$limit}
        ");

        echo json_encode([
            'success' => true,
            'data' => [
                'refunds' => $refunds ?: [],
                'total' => intval($total),
                'page' => $page,
                'limit' => $limit
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * 添加到邮件队列
 */
function addToEmailQueue($orderId, $emailType, $recipientEmail, $recipientName) {
    global $DB;

    // 检查是否已存在相同的邮件队列记录
    $existing = $DB->get_row(
        "SELECT queue_id FROM FD_sxgz_email_queue
         WHERE order_id = '{$orderId}' AND email_type = '{$emailType}'"
    );

    if ($existing) {
        return; // 已存在，不重复添加
    }

    $emailData = json_encode([
        'order_id' => $orderId,
        'timestamp' => date('Y-m-d H:i:s')
    ]);

    // 使用项目常规方法插入邮件队列
    $emailInsertData = [
        'order_id' => $orderId,
        'email_type' => $emailType,
        'recipient_email' => $recipientEmail,
        'recipient_name' => $recipientName,
        'email_data' => $emailData,
        'status' => 'pending',
        'created_at' => date('Y-m-d H:i:s')
    ];

    $fields = array_keys($emailInsertData);
    $values = array_values($emailInsertData);

    $escapedValues = array_map(function($value) use ($DB) {
        if (is_string($value)) {
            return "'" . $DB->escape($value) . "'";
        } elseif (is_null($value)) {
            return 'NULL';
        } else {
            return $value;
        }
    }, $values);

    $sql = "INSERT INTO FD_sxgz_email_queue (`" . implode('`, `', $fields) . "`) VALUES (" . implode(', ', $escapedValues) . ")";
    $DB->query($sql);
}



/**
 * 获取状态文本
 */
function getStatusText($status) {
    $statusMap = [
        'pending' => '待处理',
        'processing' => '处理中',
        'completed' => '已完成',
        'cancelled' => '已取消',
        'failed' => '失败',
        'refund_requested' => '申请退款',
        'refunded' => '已退款'
    ];

    return $statusMap[$status] ?? $status;
}

/**
 * 获取服务类型文本
 */
function getServiceTypeText($serviceType) {
    $typeMap = [
        'electronic' => '电子版',
        'mail' => '邮寄服务',
        'both' => '邮寄+电子版'
    ];

    return $typeMap[$serviceType] ?? $serviceType;
}

/**
 * 导出订单数据
 */
function exportOrders() {
    global $DB, $userrow;

    // 验证用户登录
    validateUser();

    // 检查是否为管理员
    $isAdmin = ($userrow['uid'] == 1);

    try {
        // 检查表是否存在
        $tableExistsResult = $DB->get_row("SHOW TABLES LIKE 'FD_sxgz_orders'");
        $tableExists = !empty($tableExistsResult);
        if (!$tableExists) {
            echo json_encode([
                'success' => false,
                'message' => '订单表不存在，请先安装数据库'
            ]);
            return;
        }

        // 获取筛选参数 - 过滤输入
        $exportType = isset($_GET['export_type']) ? $DB->escape(trim(strip_tags($_GET['export_type']))) : 'filtered';
        $orderIds = isset($_GET['order_ids']) ? $DB->escape(trim(strip_tags($_GET['order_ids']))) : '';
        $status = isset($_GET['status']) ? $DB->escape(trim(strip_tags($_GET['status']))) : '';
        $search = isset($_GET['search']) ? $DB->escape(trim(strip_tags($_GET['search']))) : '';
        $searchField = isset($_GET['search_field']) ? $DB->escape(trim(strip_tags($_GET['search_field']))) : '';
        $format = isset($_GET['format']) ? $DB->escape(trim(strip_tags($_GET['format']))) : 'csv'; // csv 或 excel

        // 构建查询条件
        $where = "WHERE 1=1";

        // 如果不是管理员，只能导出自己的订单
        if (!$isAdmin) {
            $where .= " AND o.uid = {$userrow['uid']}";
        }

        // 根据导出类型构建不同的查询条件
        if ($exportType === 'selected' && !empty($orderIds)) {
            // 导出选中订单
            $orderIdArray = explode(',', $orderIds);
            $orderIdArray = array_map('intval', $orderIdArray); // 确保都是整数
            $orderIdList = implode(',', $orderIdArray);
            $where .= " AND o.order_id IN ({$orderIdList})";
        } else if ($exportType === 'single') {
            // 导出单个订单
            $orderId = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
            if ($orderId > 0) {
                $where .= " AND o.order_id = {$orderId}";
            } else {
                throw new Exception('订单ID无效');
            }
        } else if ($exportType === 'filtered') {
            // 导出筛选结果
            if (!empty($status)) {
                $where .= " AND o.status = '{$DB->escape($status)}'";
            }

            if (!empty($search)) {
                $search = $DB->escape($search);
                if (!empty($searchField)) {
                    switch ($searchField) {
                        case 'uid':
                            $where .= " AND o.uid = '{$search}'";
                            break;
                        case 'oid':
                            $where .= " AND o.order_id = '{$search}'";
                            break;
                        case 'customer_name':
                            $where .= " AND o.customer_name LIKE '%{$search}%'";
                            break;
                        case 'company_name':
                            $where .= " AND o.company_name LIKE '%{$search}%'";
                            break;
                        case 'order_no':
                            $where .= " AND o.order_no LIKE '%{$search}%'";
                            break;
                        default:
                            $where .= " AND (o.order_no LIKE '%{$search}%' OR o.customer_name LIKE '%{$search}%' OR o.company_name LIKE '%{$search}%')";
                            break;
                    }
                } else {
                    $where .= " AND (o.order_no LIKE '%{$search}%' OR o.customer_name LIKE '%{$search}%' OR o.company_name LIKE '%{$search}%')";
                }
            }
        }
        // 如果是 'all' 类型，不添加额外的筛选条件

        // 查询订单数据 - 根据用户权限决定查询字段
        if ($isAdmin) {
            // 管理员可以看到所有字段 - 使用简化字段来测试
            $sql = "
                SELECT
                    o.order_id,
                    o.order_no,
                    o.uid,
                    '' as username,
                    o.customer_name,
                    o.company_name,
                    o.service_type,
                    o.status,
                    o.created_at,
                    IFNULL(o.customer_email, '') as customer_email,
                    IFNULL(o.customer_phone, '') as customer_phone,
                    IFNULL(o.customer_address, '') as customer_address,
                    IFNULL(o.material_type, '') as material_type,
                    IFNULL(o.business_license, 0) as business_license,
                    IFNULL(o.only_business_license, 0) as only_business_license,
                    IFNULL(o.print_copies, 1) as print_copies,
                    IFNULL(o.print_options, '') as print_options,
                    IFNULL(o.special_requirements, '') as special_requirements,
                    IFNULL(o.courier_company, '') as courier_company,
                    IFNULL(o.tracking_number, '') as tracking_number,
                    IFNULL(o.base_price, 0) as base_price,
                    IFNULL(o.print_price, 0) as print_price,
                    IFNULL(o.license_price, 0) as license_price,
                    IFNULL(o.total_price, 0) as total_price,
                    IFNULL(o.refund_reason, '') as refund_reason,
                    IFNULL(o.admin_notes, '') as admin_notes,
                    '' as remarks,
                    IFNULL(o.updated_at, o.created_at) as updated_at
                FROM FD_sxgz_orders o
                {$where}
                ORDER BY
                    CASE
                        WHEN o.status = 'processing' THEN 1
                        WHEN o.status = 'refund_requested' THEN 2
                        WHEN o.status = 'pending' THEN 3
                        ELSE 4
                    END ASC,
                    o.created_at DESC
            ";
        } else {
            // 普通用户只能看到基本字段，不包含敏感信息
            // 使用简化的字段列表，逐步添加字段来测试
            $sql = "
                SELECT
                    o.order_no,
                    o.customer_name,
                    o.company_name,
                    o.service_type,
                    o.status,
                    o.created_at,
                    IFNULL(o.customer_email, '') as customer_email,
                    IFNULL(o.customer_phone, '') as customer_phone,
                    IFNULL(o.customer_address, '') as customer_address,
                    IFNULL(o.material_type, '') as material_type,
                    IFNULL(o.business_license, 0) as business_license,
                    IFNULL(o.only_business_license, 0) as only_business_license,
                    IFNULL(o.print_copies, 1) as print_copies,
                    IFNULL(o.print_options, '') as print_options,
                    IFNULL(o.special_requirements, '') as special_requirements,
                    IFNULL(o.courier_company, '') as courier_company,
                    IFNULL(o.tracking_number, '') as tracking_number,
                    IFNULL(o.base_price, 0) as base_price,
                    IFNULL(o.print_price, 0) as print_price,
                    IFNULL(o.license_price, 0) as license_price,
                    IFNULL(o.total_price, 0) as total_price,
                    IFNULL(o.refund_reason, '') as refund_reason,
                    IFNULL(o.admin_notes, '') as admin_notes,
                    '' as remarks,
                    IFNULL(o.updated_at, o.created_at) as updated_at
                FROM FD_sxgz_orders o
                {$where}
                ORDER BY
                    CASE
                        WHEN o.status = 'processing' THEN 1
                        WHEN o.status = 'refund_requested' THEN 2
                        WHEN o.status = 'pending' THEN 3
                        ELSE 4
                    END ASC,
                    o.created_at DESC
            ";
        }

        $orders = $DB->get_results($sql);

        if (empty($orders)) {
            // 检查是否有任何订单数据
            $totalOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders");
            if ($totalOrders == 0) {
                echo json_encode([
                    'success' => false,
                    'message' => '系统中暂无订单数据'
                ]);
            } else if (!$isAdmin) {
                $userOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE uid = {$userrow['uid']}");
                if ($userOrders == 0) {
                    echo json_encode([
                        'success' => false,
                        'message' => '您还没有任何订单数据'
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => '没有找到符合筛选条件的订单数据'
                    ]);
                }
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '没有找到符合筛选条件的订单数据'
                ]);
            }
            return;
        }

        // 生成文件名 - 根据用户权限区分
        $filename = $isAdmin ? 'sxgz_orders_' . date('YmdHis') : 'my_sxgz_orders_' . date('YmdHis');

        if ($format === 'excel') {
            exportToExcel($orders, $filename);
        } else {
            exportToCSV($orders, $filename);
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '导出失败: ' . $e->getMessage()
        ]);
    }
}

/**
 * 导出为CSV格式
 */
function exportToCSV($orders, $filename) {
    global $userrow;

    // 设置HTTP头
    header('Content-Type: text/csv; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');

    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";

    // 打开输出流
    $output = fopen('php://output', 'w');

    // 检查是否为管理员
    $isAdmin = ($userrow['uid'] == 1);

    // CSV表头 - 根据用户权限决定
    if ($isAdmin) {
        $headers = [
            '订单ID',
            '订单号',
            '用户ID',
            '用户名',
            '客户姓名',
            '客户邮箱',
            '客户电话',
            '客户地址',
            '公司名称',
            '服务类型',
            '材料类型',
            '需要营业执照',
            '仅需营业执照',
            '打印份数',
            '打印选项',
            '特殊要求',
            '快递公司',
            '快递单号',
            '基础价格',
            '打印费用',
            '执照费用',
            '总价格',
            '订单状态',
            '退款原因',
            '管理员备注',
            '客户备注',
            '创建时间',
            '更新时间'
        ];
    } else {
        $headers = [
            '订单号',
            '客户姓名',
            '客户邮箱',
            '客户电话',
            '客户地址',
            '公司名称',
            '服务类型',
            '材料类型',
            '需要营业执照',
            '仅需营业执照',
            '打印份数',
            '打印选项',
            '特殊要求',
            '快递公司',
            '快递单号',
            '基础价格',
            '打印费用',
            '执照费用',
            '总价格',
            '订单状态',
            '退款原因',
            '管理员回复',
            '客户备注',
            '创建时间',
            '更新时间'
        ];
    }

    fputcsv($output, $headers);

    // 输出数据行 - 根据用户权限决定
    foreach ($orders as $order) {
        if ($isAdmin) {
            $row = [
                $order['order_id'],
                $order['order_no'],
                $order['uid'],
                $order['username'] ?? '',
                $order['customer_name'],
                $order['customer_email'] ?? '',
                $order['customer_phone'] ?? '',
                $order['customer_address'] ?? '',
                $order['company_name'],
                getServiceTypeText($order['service_type']),
                $order['material_type'] ?? '',
                $order['business_license'] ? '是' : '否',
                $order['only_business_license'] ? '是' : '否',
                $order['print_copies'],
                $order['print_options'] ?? '',
                $order['special_requirements'] ?? '',
                $order['courier_company'] ?? '',
                $order['tracking_number'] ?? '',
                $order['base_price'],
                $order['print_price'],
                $order['license_price'],
                $order['total_price'],
                getStatusText($order['status']),
                $order['refund_reason'] ?? '',
                $order['admin_notes'] ?? '',
                $order['remarks'] ?? '',
                $order['created_at'],
                $order['updated_at'] ?? ''
            ];
        } else {
            $row = [
                $order['order_no'],
                $order['customer_name'],
                $order['customer_email'] ?? '',
                $order['customer_phone'] ?? '',
                $order['customer_address'] ?? '',
                $order['company_name'],
                getServiceTypeText($order['service_type']),
                $order['material_type'] ?? '',
                $order['business_license'] ? '是' : '否',
                $order['only_business_license'] ? '是' : '否',
                $order['print_copies'],
                $order['print_options'] ?? '',
                $order['special_requirements'] ?? '',
                $order['courier_company'] ?? '',
                $order['tracking_number'] ?? '',
                $order['base_price'],
                $order['print_price'],
                $order['license_price'],
                $order['total_price'],
                getStatusText($order['status']),
                $order['refund_reason'] ?? '',
                $order['admin_notes'] ?? '',
                $order['remarks'] ?? '',
                $order['created_at'],
                $order['updated_at'] ?? ''
            ];
        }

        fputcsv($output, $row);
    }

    fclose($output);
    exit;
}

/**
 * 导出为Excel格式（使用HTML表格模拟）
 */
function exportToExcel($orders, $filename) {
    global $userrow;

    // 设置HTTP头
    header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');

    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";

    // 检查是否为管理员
    $isAdmin = ($userrow['uid'] == 1);

    // 开始HTML表格
    echo '<table border="1">';
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';

    if ($isAdmin) {
        echo '<td>订单ID</td>';
        echo '<td>订单号</td>';
        echo '<td>用户ID</td>';
        echo '<td>用户名</td>';
        echo '<td>客户姓名</td>';
        echo '<td>客户邮箱</td>';
        echo '<td>客户电话</td>';
        echo '<td>客户地址</td>';
        echo '<td>公司名称</td>';
        echo '<td>服务类型</td>';
        echo '<td>材料类型</td>';
        echo '<td>需要营业执照</td>';
        echo '<td>仅需营业执照</td>';
        echo '<td>打印份数</td>';
        echo '<td>打印选项</td>';
        echo '<td>特殊要求</td>';
        echo '<td>快递公司</td>';
        echo '<td>快递单号</td>';
        echo '<td>基础价格</td>';
        echo '<td>打印费用</td>';
        echo '<td>执照费用</td>';
        echo '<td>总价格</td>';
        echo '<td>订单状态</td>';
        echo '<td>退款原因</td>';
        echo '<td>管理员备注</td>';
        echo '<td>客户备注</td>';
        echo '<td>创建时间</td>';
        echo '<td>更新时间</td>';
    } else {
        echo '<td>订单号</td>';
        echo '<td>客户姓名</td>';
        echo '<td>客户邮箱</td>';
        echo '<td>客户电话</td>';
        echo '<td>客户地址</td>';
        echo '<td>公司名称</td>';
        echo '<td>服务类型</td>';
        echo '<td>材料类型</td>';
        echo '<td>需要营业执照</td>';
        echo '<td>仅需营业执照</td>';
        echo '<td>打印份数</td>';
        echo '<td>打印选项</td>';
        echo '<td>特殊要求</td>';
        echo '<td>快递公司</td>';
        echo '<td>快递单号</td>';
        echo '<td>基础价格</td>';
        echo '<td>打印费用</td>';
        echo '<td>执照费用</td>';
        echo '<td>总价格</td>';
        echo '<td>订单状态</td>';
        echo '<td>退款原因</td>';
        echo '<td>管理员回复</td>';
        echo '<td>客户备注</td>';
        echo '<td>创建时间</td>';
        echo '<td>更新时间</td>';
    }

    echo '</tr>';

    // 输出数据行 - 根据用户权限决定
    foreach ($orders as $order) {
        echo '<tr>';

        if ($isAdmin) {
            echo '<td>' . htmlspecialchars($order['order_id']) . '</td>';
            echo '<td>' . htmlspecialchars($order['order_no']) . '</td>';
            echo '<td>' . htmlspecialchars($order['uid']) . '</td>';
            echo '<td>' . htmlspecialchars($order['username'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_name']) . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_email'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_phone'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_address'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['company_name']) . '</td>';
            echo '<td>' . htmlspecialchars(getServiceTypeText($order['service_type'])) . '</td>';
            echo '<td>' . htmlspecialchars($order['material_type'] ?? '') . '</td>';
            echo '<td>' . ($order['business_license'] ? '是' : '否') . '</td>';
            echo '<td>' . ($order['only_business_license'] ? '是' : '否') . '</td>';
            echo '<td>' . htmlspecialchars($order['print_copies']) . '</td>';
            echo '<td>' . htmlspecialchars($order['print_options'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['special_requirements'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['courier_company'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['tracking_number'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['base_price']) . '</td>';
            echo '<td>' . htmlspecialchars($order['print_price']) . '</td>';
            echo '<td>' . htmlspecialchars($order['license_price']) . '</td>';
            echo '<td>' . htmlspecialchars($order['total_price']) . '</td>';
            echo '<td>' . htmlspecialchars(getStatusText($order['status'])) . '</td>';
            echo '<td>' . htmlspecialchars($order['refund_reason'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['admin_notes'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['remarks'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['created_at']) . '</td>';
            echo '<td>' . htmlspecialchars($order['updated_at'] ?? '') . '</td>';
        } else {
            echo '<td>' . htmlspecialchars($order['order_no']) . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_name']) . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_email'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_phone'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_address'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['company_name']) . '</td>';
            echo '<td>' . htmlspecialchars(getServiceTypeText($order['service_type'])) . '</td>';
            echo '<td>' . htmlspecialchars($order['material_type'] ?? '') . '</td>';
            echo '<td>' . ($order['business_license'] ? '是' : '否') . '</td>';
            echo '<td>' . ($order['only_business_license'] ? '是' : '否') . '</td>';
            echo '<td>' . htmlspecialchars($order['print_copies']) . '</td>';
            echo '<td>' . htmlspecialchars($order['print_options'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['special_requirements'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['courier_company'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['tracking_number'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['base_price']) . '</td>';
            echo '<td>' . htmlspecialchars($order['print_price']) . '</td>';
            echo '<td>' . htmlspecialchars($order['license_price']) . '</td>';
            echo '<td>' . htmlspecialchars($order['total_price']) . '</td>';
            echo '<td>' . htmlspecialchars(getStatusText($order['status'])) . '</td>';
            echo '<td>' . htmlspecialchars($order['refund_reason'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['admin_notes'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['remarks'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['created_at']) . '</td>';
            echo '<td>' . htmlspecialchars($order['updated_at'] ?? '') . '</td>';
        }

        echo '</tr>';
    }

    echo '</table>';
    exit;
}

/**
 * 获取统计数据
 */
function getStatistics() {
    global $DB, $userrow;

    // 验证管理员权限
    if (!isset($userrow) || $userrow['uid'] != 1) {
        echo json_encode(['success' => false, 'message' => '权限不足']);
        return;
    }

    try {
        // 检查表是否存在
        $tableExistsResult = $DB->get_row("SHOW TABLES LIKE 'FD_sxgz_orders'");
        $tableExists = !empty($tableExistsResult);
        if (!$tableExists) {
            echo json_encode([
                'success' => true,
                'data' => [
                    'total_orders' => 0,
                    'processing_orders' => 0,
                    'pending_orders' => 0,
                    'completed_orders' => 0,
                    'refund_requests' => 0,
                    'today_orders' => 0,
                    'total_revenue' => 0,
                    'active_users' => 0,

                    // 保持向后兼容性
                    'todayOrders' => 0,
                    'todayIncome' => '0.00',
                    'pendingOrders' => 0,
                    'totalOrders' => 0
                ]
            ]);
            return;
        }

        $today = date('Y-m-d');

        // 总订单数
        $totalOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders") ?: 0;

        // 处理中订单数
        $processingOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE status = 'processing'") ?: 0;

        // 待处理订单数
        $pendingOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE status = 'pending'") ?: 0;

        // 已完成订单数
        $completedOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE status = 'completed'") ?: 0;

        // 申请退款订单数
        $refundRequests = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE status = 'refund_requested'") ?: 0;

        // 今日订单数
        $todayOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE DATE(created_at) = '{$today}'") ?: 0;

        // 总收入（排除已取消和已退款的订单）
        $totalRevenue = $DB->get_var("SELECT COALESCE(SUM(total_price), 0) FROM FD_sxgz_orders WHERE status NOT IN ('cancelled', 'refunded')") ?: 0;

        // 活跃用户数（最近30天有订单的用户）
        $activeUsers = $DB->get_var("SELECT COUNT(DISTINCT uid) FROM FD_sxgz_orders WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)") ?: 0;

        echo json_encode([
            'success' => true,
            'data' => [
                'total_orders' => intval($totalOrders),
                'processing_orders' => intval($processingOrders),
                'pending_orders' => intval($pendingOrders),
                'completed_orders' => intval($completedOrders),
                'refund_requests' => intval($refundRequests),
                'today_orders' => intval($todayOrders),
                'total_revenue' => floatval($totalRevenue),
                'active_users' => intval($activeUsers),

                // 保持向后兼容性
                'todayOrders' => intval($todayOrders),
                'todayIncome' => number_format(floatval($totalRevenue), 2),
                'pendingOrders' => intval($pendingOrders),
                'totalOrders' => intval($totalOrders)
            ]
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '获取统计数据失败: ' . $e->getMessage(),
            'debug' => [
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => basename($e->getFile())
            ]
        ]);
    }
}

/**
 * 获取管理员统计数据
 */
function getAdminStats() {
    global $DB, $userrow;

    // 验证管理员权限
    if (!isset($userrow) || $userrow['uid'] != 1) {
        echo json_encode(['success' => false, 'message' => '权限不足']);
        return;
    }

    try {
        // 检查表是否存在
        $tableExistsResult = $DB->get_row("SHOW TABLES LIKE 'FD_sxgz_orders'");
        $tableExists = !empty($tableExistsResult);
        if (!$tableExists) {
            echo json_encode([
                'success' => true,
                'data' => [
                    'overview' => [
                        'total_orders' => 0,
                        'pending_orders' => 0,
                        'processing_orders' => 0,
                        'completed_orders' => 0,
                        'delivered_orders' => 0,
                        'cancelled_orders' => 0,
                        'today_orders' => 0
                    ],
                    'revenue' => [
                        'total' => 0,
                        'today' => 0,
                        'month' => 0
                    ]
                ]
            ]);
            return;
        }

        // 基础统计
        $totalOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders") ?: 0;
        $pendingOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE status = 'pending'") ?: 0;
        $processingOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE status = 'processing'") ?: 0;
        $completedOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE status = 'completed'") ?: 0;
        $deliveredOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE status = 'delivered'") ?: 0;
        $cancelledOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE status = 'cancelled'") ?: 0;

        // 收入统计
        $totalRevenue = $DB->get_var("SELECT SUM(total_price) FROM FD_sxgz_orders WHERE status IN ('completed', 'delivered')") ?: 0;
        $todayRevenue = $DB->get_var("SELECT SUM(total_price) FROM FD_sxgz_orders WHERE status IN ('completed', 'delivered') AND DATE(created_at) = CURDATE()") ?: 0;
        $monthRevenue = $DB->get_var("SELECT SUM(total_price) FROM FD_sxgz_orders WHERE status IN ('completed', 'delivered') AND YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())") ?: 0;

        // 今日新订单
        $todayOrders = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE DATE(created_at) = CURDATE()") ?: 0;

        echo json_encode([
            'success' => true,
            'data' => [
                'overview' => [
                    'total_orders' => intval($totalOrders),
                    'pending_orders' => intval($pendingOrders),
                    'processing_orders' => intval($processingOrders),
                    'completed_orders' => intval($completedOrders),
                    'delivered_orders' => intval($deliveredOrders),
                    'cancelled_orders' => intval($cancelledOrders),
                    'today_orders' => intval($todayOrders)
                ],
                'revenue' => [
                    'total' => floatval($totalRevenue),
                    'today' => floatval($todayRevenue),
                    'month' => floatval($monthRevenue)
                ]
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '获取统计数据失败: ' . $e->getMessage(),
            'debug' => [
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => basename($e->getFile())
            ]
        ]);
    }
}

/**
 * 获取用户等级费率信息
 */
function getUserRates() {
    try {
        // 调用外部API获取用户等级费率
        $apiUrl = $_SERVER['DOCUMENT_ROOT'] . '/apisub.php?act=djlist';

        // 使用curl代替file_get_contents
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost/apisub.php?act=djlist');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($response === false || $httpCode !== 200) {
            throw new Exception('无法获取费率信息，HTTP状态码: ' . $httpCode);
        }

        $rateData = json_decode($response, true);

        if (!$rateData || $rateData['code'] != 1) {
            // 如果API调用失败，返回默认费率数据
            $defaultRates = [
                ['id' => '1', 'name' => '超级管理员', 'rate' => '0.200'],
                ['id' => '2', 'name' => '金牌代理', 'rate' => '0.300'],
                ['id' => '3', 'name' => '钻石代理', 'rate' => '0.400'],
                ['id' => '4', 'name' => '黄金代理', 'rate' => '0.500'],
                ['id' => '5', 'name' => '白银代理', 'rate' => '0.600'],
                ['id' => '6', 'name' => '普通代理', 'rate' => '0.800']
            ];

            echo json_encode([
                'success' => true,
                'data' => $defaultRates,
                'note' => '使用默认费率数据'
            ]);
            return;
        }

        echo json_encode([
            'success' => true,
            'data' => $rateData['data']
        ]);

    } catch (Exception $e) {
        // 返回默认费率数据作为备用
        $defaultRates = [
            ['id' => '1', 'name' => '超级管理员', 'rate' => '0.200'],
            ['id' => '2', 'name' => '金牌代理', 'rate' => '0.300'],
            ['id' => '3', 'name' => '钻石代理', 'rate' => '0.400'],
            ['id' => '4', 'name' => '黄金代理', 'rate' => '0.500'],
            ['id' => '5', 'name' => '白银代理', 'rate' => '0.600'],
            ['id' => '6', 'name' => '普通代理', 'rate' => '0.800']
        ];

        echo json_encode([
            'success' => true,
            'data' => $defaultRates,
            'error' => $e->getMessage(),
            'note' => '使用默认费率数据'
        ]);
    }
}

/**
 * 管理员获取公司列表（用于管理）
 */
function getCompaniesForAdmin() {
    global $DB, $userrow;

    // 验证管理员权限
    if (!isset($userrow) || $userrow['uid'] != 1) {
        echo json_encode(['success' => false, 'message' => '权限不足']);
        return;
    }

    try {
        // 过滤输入参数
        $page = intval($_GET['page'] ?? 1);
        $limit = intval($_GET['limit'] ?? 20);
        $search = isset($_GET['search']) ? $DB->escape(trim(strip_tags($_GET['search']))) : '';

        $offset = ($page - 1) * $limit;
        $where = "WHERE 1=1";

        // 默认只显示实习盖章分类的公司
        $sxgzCategory = $DB->get_row("SELECT id FROM qingka_wangke_fenlei WHERE name = '实习盖章' LIMIT 1");
        if ($sxgzCategory) {
            $where .= " AND fenlei = '{$sxgzCategory['id']}'";
        }

        // 搜索条件
        if (!empty($search)) {
            $where .= " AND (name LIKE '%{$search}%' OR content LIKE '%{$search}%')";
        }

        // 获取总数
        $total = $DB->get_var("SELECT COUNT(*) FROM qingka_wangke_class {$where}");

        // 获取公司列表
        $companies = $DB->get_results("
            SELECT cid, name, price, content, fenlei, addtime, status
            FROM qingka_wangke_class
            {$where}
            ORDER BY addtime DESC
            LIMIT {$offset}, {$limit}
        ");

        // 转换为数组格式
        $companiesArray = [];
        if ($companies) {
            foreach ($companies as $company) {
                $companyArray = (array)$company;
                $companiesArray[] = $companyArray;
            }
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'companies' => $companiesArray,
                'total' => intval($total),
                'page' => $page,
                'limit' => $limit
            ],
            'debug' => [
                'where_clause' => $where,
                'company_count' => count($companiesArray),
                'raw_companies' => count($companies ?: []),
                'search_params' => [
                    'page' => $page,
                    'limit' => $limit,
                    'search' => $search
                ],
                'sql_query' => "SELECT cid, name, price, content, fenlei, addtime, status FROM qingka_wangke_class {$where} ORDER BY addtime DESC LIMIT {$offset}, {$limit}"
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '获取公司列表失败: ' . $e->getMessage()]);
    }
}

/**
 * 添加公司
 */
function addCompany() {
    global $DB, $userrow;

    // 验证管理员权限
    if (!isset($userrow) || $userrow['uid'] != 1) {
        echo json_encode(['success' => false, 'message' => '权限不足']);
        return;
    }

    try {
        // 获取请求数据，支持JSON和表单数据
        $rawInput = file_get_contents('php://input');
        $input = json_decode($rawInput, true);

        if ($input) {
            // JSON数据 - 过滤输入参数
            $name = isset($input['name']) ? $DB->escape(trim(strip_tags($input['name']))) : '';
            $price = floatval($input['price'] ?? 0);
            $content = isset($input['content']) ? $DB->escape(trim(strip_tags($input['content']))) : '';
            $status = intval($input['status'] ?? 1);
            $dataSource = 'JSON';
        } else {
            // 表单数据 - 过滤输入参数
            $name = isset($_POST['name']) ? $DB->escape(trim(strip_tags($_POST['name']))) : '';
            $price = floatval($_POST['price'] ?? 0);
            $content = isset($_POST['content']) ? $DB->escape(trim(strip_tags($_POST['content']))) : '';
            $status = intval($_POST['status'] ?? 1);
            $dataSource = 'POST';
        }

        // 验证数据

        // 自动获取实习盖章分类ID
        $sxgzCategory = $DB->get_row("SELECT id FROM qingka_wangke_fenlei WHERE name = '实习盖章' LIMIT 1");
        $fenlei = $sxgzCategory ? intval($sxgzCategory['id']) : 0;

        // 验证必填字段
        if (empty($name)) {
            throw new Exception('公司名称不能为空');
        }

        if ($price <= 0) {
            throw new Exception('价格必须大于0');
        }

        // 检查公司名称是否已存在
        $existing = $DB->get_var("SELECT COUNT(*) FROM qingka_wangke_class WHERE name = '{$name}'");
        if ($existing > 0) {
            throw new Exception('公司名称已存在');
        }

        // 插入新公司 - 使用项目常规方法
        $companyInsertData = [
            'name' => $name,
            'price' => $price,
            'content' => $content,
            'fenlei' => $fenlei,
            'status' => $status,
            'addtime' => time() // 使用时间戳，符合项目规范
        ];

        $fields = array_keys($companyInsertData);
        $values = array_values($companyInsertData);

        $escapedValues = array_map(function($value) use ($DB) {
            if (is_string($value)) {
                return "'" . $DB->escape($value) . "'";
            } elseif (is_null($value)) {
                return 'NULL';
            } else {
                return $value;
            }
        }, $values);

        $sql = "INSERT INTO qingka_wangke_class (`" . implode('`, `', $fields) . "`) VALUES (" . implode(', ', $escapedValues) . ")";
        $result = $DB->query($sql);

        if ($result) {
            // 获取插入的ID
            $insertedRecord = $DB->get_row("SELECT cid FROM qingka_wangke_class WHERE name = '{$DB->escape($name)}' ORDER BY cid DESC LIMIT 1");
            $insertId = $insertedRecord ? intval($insertedRecord['cid']) : 0;

            echo json_encode([
                'success' => true,
                'message' => '公司添加成功',
                'data' => ['cid' => $insertId]
            ]);
        } else {
            throw new Exception('添加公司失败: ' . $DB->error());
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * 更新公司信息
 */
function updateCompany() {
    global $DB, $userrow;

    // 验证管理员权限
    if (!isset($userrow) || $userrow['uid'] != 1) {
        echo json_encode(['success' => false, 'message' => '权限不足']);
        return;
    }

    try {
        // 获取请求数据，支持JSON和表单数据
        $input = json_decode(file_get_contents('php://input'), true);
        if ($input) {
            // JSON数据 - 过滤输入参数
            $cid = intval($input['cid'] ?? 0);
            $name = isset($input['name']) ? $DB->escape(trim(strip_tags($input['name']))) : '';
            $price = floatval($input['price'] ?? 0);
            $content = isset($input['content']) ? $DB->escape(trim(strip_tags($input['content']))) : '';
            $status = intval($input['status'] ?? 1);
        } else {
            // 表单数据 - 过滤输入参数
            $cid = intval($_POST['cid'] ?? 0);
            $name = isset($_POST['name']) ? $DB->escape(trim(strip_tags($_POST['name']))) : '';
            $price = floatval($_POST['price'] ?? 0);
            $content = isset($_POST['content']) ? $DB->escape(trim(strip_tags($_POST['content']))) : '';
            $status = intval($_POST['status'] ?? 1);
        }

        // 自动获取实习盖章分类ID
        $sxgzCategory = $DB->get_row("SELECT id FROM qingka_wangke_fenlei WHERE name = '实习盖章' LIMIT 1");
        $fenlei = $sxgzCategory ? intval($sxgzCategory['id']) : 0;

        // 验证必填字段
        if ($cid <= 0) {
            throw new Exception('公司ID无效');
        }

        if (empty($name)) {
            throw new Exception('公司名称不能为空');
        }

        if ($price <= 0) {
            throw new Exception('价格必须大于0');
        }

        // 检查公司是否存在
        $existing = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid = {$cid}");
        if (!$existing) {
            throw new Exception('公司不存在');
        }

        // 转义数据防止SQL注入
        $escapedName = $DB->escape($name);
        $escapedContent = $DB->escape($content);

        // 检查公司名称是否与其他公司重复
        $duplicate = $DB->get_var("SELECT COUNT(*) FROM qingka_wangke_class WHERE name = '{$escapedName}' AND cid != {$cid}");
        if ($duplicate > 0) {
            throw new Exception('公司名称已被其他公司使用');
        }

        // 更新公司信息
        $updateSql = "UPDATE qingka_wangke_class SET
                        name = '{$escapedName}',
                        price = {$price},
                        content = '{$escapedContent}',
                        fenlei = {$fenlei},
                        status = {$status}
                      WHERE cid = {$cid}";

        $result = $DB->query($updateSql);

        if ($result) {
            // 检查是否真的更新了数据
            $affectedRows = $DB->affected();
            echo json_encode([
                'success' => true,
                'message' => '公司信息更新成功',
                'debug' => [
                    'affected_rows' => $affectedRows,
                    'sql' => $updateSql
                ]
            ]);
        } else {
            throw new Exception('更新公司信息失败: ' . $DB->error());
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * 删除公司
 */
function deleteCompany() {
    global $DB, $userrow;

    // 验证管理员权限
    if (!isset($userrow) || $userrow['uid'] != 1) {
        echo json_encode(['success' => false, 'message' => '权限不足']);
        return;
    }

    try {
        // 获取请求数据，支持JSON和表单数据
        $input = json_decode(file_get_contents('php://input'), true);
        if ($input) {
            // JSON数据
            $cid = intval($input['cid'] ?? 0);
        } else {
            // 表单数据
            $cid = intval($_POST['cid'] ?? 0);
        }

        if ($cid <= 0) {
            throw new Exception('公司ID无效');
        }

        // 检查公司是否存在
        $existing = $DB->get_row("SELECT * FROM qingka_wangke_class WHERE cid = {$cid}");
        if (!$existing) {
            throw new Exception('公司不存在');
        }

        // 检查是否有相关订单
        $orderCount = $DB->get_var("SELECT COUNT(*) FROM FD_sxgz_orders WHERE company_id = {$cid}");
        if ($orderCount > 0) {
            throw new Exception('该公司有相关订单，无法删除');
        }

        // 删除公司
        $result = $DB->query("DELETE FROM qingka_wangke_class WHERE cid = {$cid}");

        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => '公司删除成功'
            ]);
        } else {
            throw new Exception('删除公司失败');
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * 删除订单（管理员功能）
 */
function deleteOrder() {
    global $DB, $userrow;

    try {
        // 验证管理员权限
        if (!isset($userrow) || $userrow['uid'] != 1) {
            throw new Exception('权限不足');
        }

        // 获取POST数据
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }

        $orderId = trim(strip_tags(addslashes($input['order_id'] ?? '')));

        if (empty($orderId)) {
            throw new Exception('订单ID不能为空');
        }

        // 验证订单是否存在
        $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");
        if (!$order) {
            throw new Exception('订单不存在');
        }

        // 删除订单相关的所有文件
        try {
            // 构建文件路径
            $uploadDir = __DIR__ . '/uploads/uid_' . $order['uid'] . '/orderid_' . $orderId . '/';
            $processedDir = __DIR__ . '/processed/uid_' . $order['uid'] . '/orderid_' . $orderId . '/';

            // 删除上传文件目录
            if (is_dir($uploadDir)) {
                deleteDirectory($uploadDir);
            }

            // 删除处理文件目录
            if (is_dir($processedDir)) {
                deleteDirectory($processedDir);
            }

        } catch (Exception $e) {
            // 文件删除失败不影响订单删除，记录日志即可
            error_log("删除订单文件失败: " . $e->getMessage());
        }

        // 删除邮件队列中相关的记录
        $DB->query("DELETE FROM FD_sxgz_email_queue WHERE order_id = '{$orderId}'");

        // 删除订单记录
        $result = $DB->query("DELETE FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");

        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => '订单删除成功'
            ]);
        } else {
            throw new Exception('删除订单失败');
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * 递归删除目录及其内容
 */
function deleteDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }

    $files = array_diff(scandir($dir), array('.', '..'));
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            deleteDirectory($path);
        } else {
            unlink($path);
        }
    }

    return rmdir($dir);
}

/**
 * 更新订单状态
 */
function updateOrderStatus() {
    global $DB, $userrow;

    try {
        // 验证管理员权限
        if (!isset($userrow) || $userrow['uid'] != 1) {
            throw new Exception('权限不足');
        }

        // 获取POST数据
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }

        $orderId = trim(strip_tags(addslashes($input['order_id'] ?? '')));
        $status = trim(strip_tags(addslashes($input['status'] ?? '')));
        $notes = trim(strip_tags(addslashes($input['notes'] ?? '')));

        if (empty($orderId)) {
            throw new Exception('订单ID不能为空');
        }

        if (empty($status)) {
            throw new Exception('状态不能为空');
        }

        // 验证状态值
        $validStatuses = ['pending', 'processing', 'completed', 'failed', 'refund_requested', 'refunded'];
        if (!in_array($status, $validStatuses)) {
            throw new Exception('无效的状态值');
        }

        // 验证订单是否存在
        $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}'");
        if (!$order) {
            throw new Exception('订单不存在');
        }

        // 构建更新SQL - 不再更新备注
        $updateFields = ["status = '{$status}'", "updated_at = NOW()"];

        // 如果状态是完成，设置完成时间
        if ($status === 'completed') {
            $updateFields[] = "completed_at = NOW()";
        }

        $updateSql = "UPDATE FD_sxgz_orders SET " . implode(', ', $updateFields) . " WHERE order_id = '{$orderId}'";

        $result = $DB->query($updateSql);

        if ($result) {
            // 使用wlog记录操作日志，而不是写入备注
            $statusText = '';
            switch ($status) {
                case 'processing': $statusText = '开始处理'; break;
                case 'completed': $statusText = '完成'; break;
                case 'failed': $statusText = '标记失败'; break;
                default: $statusText = $status; break;
            }

            // 记录到系统日志
            wlog("订单状态更新", "管理员更新订单 {$orderId} 状态为 {$statusText}" . (!empty($notes) ? "，备注：{$notes}" : ""));

            echo json_encode([
                'success' => true,
                'message' => '订单状态更新成功',
                'order_id' => $orderId,
                'new_status' => $status
            ]);
        } else {
            wlog("订单状态更新失败", "订单 {$orderId} 状态更新失败: " . $DB->error());
            throw new Exception('更新订单状态失败: ' . $DB->error());
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * 处理退款申请
 */
function handleRefund() {
    global $DB, $userrow;

    try {
        // 验证管理员权限
        if (!isset($userrow) || $userrow['uid'] != 1) {
            throw new Exception('权限不足');
        }

        // 获取POST数据
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }

        $orderId = trim(strip_tags(addslashes($input['order_id'] ?? '')));
        $action = trim(strip_tags(addslashes($input['action'] ?? '')));
        $notes = trim(strip_tags(addslashes($input['notes'] ?? '')));

        if (empty($orderId)) {
            throw new Exception('订单ID不能为空');
        }

        if (empty($action) || !in_array($action, ['approve', 'reject'])) {
            throw new Exception('无效的操作类型');
        }

        // 验证订单是否存在且状态为申请退款
        $order = $DB->get_row("SELECT * FROM FD_sxgz_orders WHERE order_id = '{$orderId}' AND status = 'refund_requested'");
        if (!$order) {
            throw new Exception('订单不存在或状态不正确');
        }

        // 更新订单状态 - 不再更新备注
        $newStatus = $action === 'approve' ? 'refunded' : 'completed';
        $actionText = $action === 'approve' ? '批准退款' : '拒绝退款';

        $updateSql = "UPDATE FD_sxgz_orders SET
                      status = '{$newStatus}',
                      updated_at = NOW()
                      WHERE order_id = '{$orderId}'";

        $result = $DB->query($updateSql);

        if ($result) {
            // 使用wlog记录操作日志，而不是写入备注
            $logMessage = "管理员{$actionText}订单 {$orderId}";
            if (!empty($notes)) {
                $logMessage .= "，备注：{$notes}";
            }
            wlog("退款处理", $logMessage);

            echo json_encode([
                'success' => true,
                'message' => $actionText . '成功',
                'order_id' => $orderId,
                'action' => $action
            ]);
        } else {
            wlog("退款处理失败", "订单 {$orderId} {$actionText}失败: " . $DB->error());
            throw new Exception($actionText . '失败: ' . $DB->error());
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * 为代理商提供公司列表
 */
function getCompaniesForAgent() {
    global $DB;

    // 验证代理商身份
    $uid = $_GET['uid'] ?? '';
    $key = $_GET['key'] ?? '';

    if (empty($uid) || empty($key)) {
        throw new Exception('缺少认证参数', 401);
    }

    // 验证用户存在且key正确
    $user = $DB->get_row("SELECT * FROM qingka_wangke_user WHERE uid = '{$uid}' AND `key` = '{$key}' AND active = 1");

    if (!$user) {
        throw new Exception('代理商认证失败', 401);
    }

    try {
        // 获取实习盖章分类ID
        $categoryRow = $DB->get_row("SELECT id FROM qingka_wangke_fenlei WHERE name = '实习盖章' LIMIT 1");
        if (!$categoryRow) {
            throw new Exception('实习盖章分类不存在');
        }

        $categoryId = $categoryRow['id'];

        // 获取实习盖章分类的公司
        $companies = $DB->get_results("SELECT cid, name, price, content, status, fenlei, addtime FROM qingka_wangke_class WHERE fenlei = '{$categoryId}' AND status = 1 ORDER BY sort ASC, name ASC");

        // 转换为数组格式
        $companiesArray = [];
        if ($companies) {
            foreach ($companies as $company) {
                $companiesArray[] = (array)$company;
            }
        }

        echo json_encode([
            'success' => true,
            'data' => $companiesArray,
            'message' => '获取公司列表成功',
            'agent_uid' => $uid,
            'timestamp' => date('Y-m-d H:i:s')
        ]);

    } catch (Exception $e) {
        throw new Exception('获取公司列表失败: ' . $e->getMessage());
    }
}
?>