实习盖章无限对接插件包 - 快速安装指南
=============================================

📦 插件包内容
-----------
✅ apitaowa.php                   - 对接API（接收下级插件）
✅ index/sxgz.php                 - 用户下单界面（支持实时刷新公司列表）
✅ sxgz/upstream.php.template     - 上游配置模板
✅ sxgz/api.php                   - 业务API（支持上游转发和公司列表缓存）
✅ sxgz/sync.php                  - 订单同步脚本
✅ sxgz/install.sql               - 数据库安装脚本
✅ sxgz/file_manager.php          - 文件管理类
✅ sxgz/email_sender.php          - 邮件发送功能
✅ sxgz/email_templates.php       - 邮件模板

🚀 安装模式选择
--------------

模式1：独立运行（源台模式）
------------------------
适用场景：作为业务源头，不需要对接上游

安装步骤：
1. 上传插件包到网站根目录
2. 执行数据库安装：
   mysql -u username -p database_name < sxgz/install.sql
3. 设置目录权限：
   chmod 755 sxgz/uploads/ sxgz/processed/ sxgz/logs/
   chown -R www-data:www-data sxgz/uploads/ sxgz/processed/ sxgz/logs/
4. 访问系统：
   - 用户界面：/index/sxgz.php
   - 对接API：/apitaowa.php

模式2：对接上游（代理商模式）  
---------------------------
适用场景：作为代理商，对接到上级插件或源台

安装步骤：
1. 配置上游信息：
   cp sxgz/upstream.php.template sxgz/upstream.php
   vi sxgz/upstream.php
   
   修改以下3个参数：
   - upstream_url: 上游的apitaowa.php地址
   - upstream_uid: 您在上游的用户ID  
   - upstream_key: 您在上游的API密钥

   示例配置：
   ```php
   return [
       'upstream_url' => 'https://source.domain.com',  // 只需要域名
       'upstream_uid' => 123,
       'upstream_key' => 'abc123def456',
       'agent_name' => '我的代理商',
   ];
   ```

2. 执行数据库安装：
   mysql -u username -p database_name < sxgz/install.sql

3. 设置目录权限：
   chmod 755 sxgz/uploads/ sxgz/processed/ sxgz/logs/
   chown -R www-data:www-data sxgz/uploads/ sxgz/processed/ sxgz/logs/

4. 设置定时同步（可选）：
   crontab -e
   添加：*/5 * * * * php /path/to/sxgz/sync.php

5. 访问系统：
   - 用户界面：/index/sxgz.php（支持实时刷新公司列表）
   - 对接API：/apitaowa.php（供下级插件对接）

🎉 安装完成！
-----------
访问地址：
- 用户界面：/index/sxgz.php
- 对接API：/apitaowa.php（供下级插件对接）

注意：插件包不包含管理界面，所有订单最终汇总到源台处理

📋 可选配置
----------

1. 设置定时同步（推荐）：
   crontab -e
   添加：*/5 * * * * php /path/to/sxgz/sync.php

2. 如果您要成为上游，无需额外配置
   下级代理商可直接对接您的 apitaowa.php

🔄 工作原理
----------

独立模式：
- 用户下单 → 本地处理 → 完成订单
- 接收下级对接 → 处理下级订单

对接模式：
- 用户下单 → 转发上游 → 状态同步
- 同时可接收下级对接

无限对接链：
插件A ← 插件B ← 插件C ← 插件D...
每个插件都可以继续分发和接收对接

❓ 获取配置信息
-------------
- upstream_uid: 登录上游系统查看个人中心
- upstream_key: 在上游系统的对接信息中查看
- 如无KEY请联系上游管理员开通

📞 技术支持
----------
如有问题请联系上游管理员或查看 README.md 详细文档

常见问题：
1. 订单转发失败 → 检查上游URL、UID、KEY
2. 同步不工作 → 确认定时任务和权限
3. 权限不足 → 检查目录权限设置

版本：v1.0 | 更新：2025-01-16
