<?php
/**
 * 实习盖章模块API接口
 * 提供订单创建、查询、更新等功能
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入必要的文件
require_once('../confing/common.php');
require_once('file_manager.php');

// 添加数据库方法兼容性
if (!method_exists($DB, 'get_var')) {
    // 如果没有get_var方法，添加一个兼容方法
    class DBWrapper {
        private $db;

        public function __construct($db) {
            $this->db = $db;
        }

        public function get_var($query) {
            $result = $this->db->get_row($query);
            if ($result && is_array($result)) {
                $values = array_values($result);
                return $values[0];
            }
            return null;
        }

        public function __get($property) {
            // 处理属性访问，如 insert_id
            if (isset($this->db->$property)) {
                return $this->db->$property;
            }
            return null;
        }

        public function __call($method, $args) {
            if (method_exists($this->db, $method)) {
                return $this->db->$method(...$args);
            }
            return null;
        }
    }

    $DB = new DBWrapper($DB);
}

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 获取请求参数
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$method = $_SERVER['REQUEST_METHOD'];

// 路由处理
try {
    switch ($action) {
        case 'create_order':
            if ($method === 'POST') {
                createOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'upload_file':
            if ($method === 'POST') {
                uploadFile();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_orders':
            if ($method === 'GET') {
                getOrders();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_order':
            if ($method === 'GET') {
                getOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'update_order':
            if ($method === 'POST') {
                updateOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'delete_order':
            if ($method === 'POST') {
                deleteOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'download_file':
            if ($method === 'GET') {
                downloadFile();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'admin_orders':
            if ($method === 'GET') {
                getAdminOrders();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'admin_update':
            if ($method === 'POST') {
                adminUpdateOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_statistics':
            if ($method === 'GET') {
                getStatistics();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_admin_stats':
            if ($method === 'GET') {
                getAdminStats();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_user_rates':
            if ($method === 'GET') {
                getUserRates();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;





        case 'delete_file':
            if ($method === 'POST') {
                deleteFile();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'delete_order':
            if ($method === 'POST') {
                deleteOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'apply_refund':
            if ($method === 'POST') {
                applyRefund();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'process_refund':
            if ($method === 'POST') {
                processRefund();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'update_order_status':
            if ($method === 'POST') {
                updateOrderStatus();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'handle_refund':
            if ($method === 'POST') {
                handleRefund();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'process_failed_order':
            if ($method === 'POST') {
                processFailedOrder();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_refund_requests':
            if ($method === 'GET') {
                getRefundRequests();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'export_orders':
            if ($method === 'GET') {
                exportOrders();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_companies':
            if ($method === 'GET') {
                // 检查是否是管理员请求
                if (isset($_GET['admin']) && $_GET['admin'] == '1') {
                    getCompaniesForAdmin();
                } else {
                    getCompanies();
                }
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'get_companies_for_agent':
            if ($method === 'GET') {
                getCompaniesForAgent(); // 代理商专用接口，不检查本地用户登录
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        case 'refresh_companies':
            if ($method === 'POST') {
                refreshCompanies();
            } else {
                throw new Exception('Method not allowed', 405);
            }
            break;

        default:
            throw new Exception('Invalid action', 400);
    }
} catch (Exception $e) {
    http_response_code($e->getCode() ?: 500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'code' => $e->getCode()
    ]);
}

/**
 * 验证用户登录 - 测试模式，跳过验证
 */
function validateUser($requireAdmin = false) {
    global $userrow;

    // 测试模式：如果没有登录用户，创建一个默认用户
    if (!isset($userrow) || !$userrow) {
        $userrow = [
            'uid' => 1,
            'user' => 'test_user',
            'name' => '测试用户',
            'addprice' => 1.0,
            'money' => 1000.00
        ];
    }

    if ($requireAdmin && $userrow['uid'] != 1) {
        throw new Exception('权限不足', 403);
    }

    return $userrow;
}

/**
 * 获取上游配置文件路径
 */
function getUpstreamConfigPath() {
    $possiblePaths = [
        'sxgz/upstream.php',                    // 从网站根目录
        __DIR__ . '/upstream.php',              // 从当前API文件目录
        dirname(__FILE__) . '/upstream.php'     // 从当前文件目录
    ];

    foreach ($possiblePaths as $path) {
        if (file_exists($path)) {
            return $path;
        }
    }

    return null;
}

/**
 * 获取缓存文件路径
 */
function getCacheFilePath($filename) {
    $possibleDirs = [
        'sxgz/',                    // 从网站根目录
        __DIR__ . '/',              // 从当前API文件目录
        dirname(__FILE__) . '/'     // 从当前文件目录
    ];

    foreach ($possibleDirs as $dir) {
        if (is_dir($dir) || $dir === 'sxgz/') {
            return $dir . $filename;
        }
    }

    // 默认返回当前目录
    return __DIR__ . '/' . $filename;
}

/**
 * 创建订单 - 纯对接模式
 */
function createOrder() {
    global $DB, $userrow;

    // 插件包必须配置上游才能使用
    $upstreamConfigFile = getUpstreamConfigPath();
    if (!$upstreamConfigFile) {
        throw new Exception('插件包必须配置上游才能使用，请先配置 upstream.php 文件', 400);
    }

    $upstreamConfig = include($upstreamConfigFile);
    if (empty($upstreamConfig['upstream_url']) || empty($upstreamConfig['upstream_uid']) || empty($upstreamConfig['upstream_key'])) {
        throw new Exception('上游配置不完整，请检查 upstream.php 文件中的配置参数', 400);
    }

    // 转发到上游
    createOrderWithUpstream($upstreamConfig);
}

/**
 * 保存本地订单记录（用于代理端订单列表）
 */
function saveLocalOrderRecord($orderData, $upstreamResult, $user) {
    global $DB;

    // 检查表是否存在
    $tableExists = $DB->get_row("SHOW TABLES LIKE 'fd_sxgz_orders'");
    if (!$tableExists) {
        throw new Exception('订单表不存在，请先执行数据库安装脚本: mysql -u username -p database_name < sxgz/install.sql');
    }

    // 记录调试信息

    // 准备本地订单数据（只使用数据库表中存在的字段）
    $localOrderData = [
        'uid' => $user['uid'],
        'order_no' => $upstreamResult['order_no'],
        'service_type' => $orderData['service_type'] ?? 'electronic',
        'company_id' => intval($orderData['company_id'] ?? 0),
        'company_name' => $orderData['company_name'] ?? ('公司ID: ' . ($orderData['company_id'] ?? 'unknown')),
        'material_type' => $orderData['material_type'] ?? 'upload',
        'business_license' => intval($orderData['business_license'] ?? 0),
        'only_business_license' => intval($orderData['only_business_license'] ?? 0),
        'customer_name' => $orderData['customer_name'] ?? '未提供姓名',
        'customer_email' => $orderData['customer_email'] ?? '',
        'customer_phone' => $orderData['customer_phone'] ?? '',
        'customer_address' => $orderData['customer_address'] ?? '',
        'courier_company' => $orderData['courier_company'] ?? '',
        'tracking_number' => $orderData['tracking_number'] ?? '',
        'print_copies' => intval($orderData['print_copies'] ?? 0),
        'special_requirements' => $orderData['special_requirements'] ?? '',
        // 价格字段 - 使用正确的字段名
        'base_price' => floatval($upstreamResult['total_price'] ?? 0),
        'print_price' => 0.00,
        'license_price' => 0.00,
        'total_price' => floatval($upstreamResult['total_price'] ?? 0),
        'status' => 'pending',
        'source' => 'direct', // 标记为直接订单（符合数据库枚举）
        'created_at' => date('Y-m-d H:i:s'),
        'admin_notes' => '上游订单ID: ' . ($upstreamResult['order_id'] ?? ''),
        'agent_order_id' => intval($upstreamResult['order_id'] ?? 0) // 专门存储上游订单ID
    ];

    // 验证必要字段
    $requiredFields = ['uid', 'order_no', 'service_type', 'company_id', 'company_name', 'customer_name'];
    foreach ($requiredFields as $field) {
        if (empty($localOrderData[$field])) {
            throw new Exception("本地订单记录缺少必要字段: {$field}, 值: " . ($localOrderData[$field] ?? 'NULL'));
        }
    }


    // 构建插入SQL
    $fields = array_keys($localOrderData);
    $values = array_values($localOrderData);

    // 转义字符串值
    $escapedValues = [];
    foreach ($values as $value) {
        if (is_null($value)) {
            $escapedValues[] = 'NULL';
        } elseif (is_numeric($value)) {
            $escapedValues[] = $value;
        } else {
            $escapedValues[] = "'" . $DB->escape($value) . "'";
        }
    }

    $sql = "INSERT INTO fd_sxgz_orders (`" . implode('`, `', $fields) . "`) VALUES (" . implode(', ', $escapedValues) . ")";

    // 记录调试信息

    // 插入本地订单记录
    $result = $DB->query($sql);


    if (!$result) {
        // 获取数据库错误信息
        $dbError = $DB->error() ?: '未知数据库错误';
        // 记录详细的调试信息
        throw new Exception('保存本地订单记录失败: ' . $dbError);
    }

    // 获取插入的ID - 通过查询获取
    $insertedRecord = $DB->get_row("SELECT order_id FROM fd_sxgz_orders WHERE order_no = '{$localOrderData['order_no']}' LIMIT 1");
    $localOrderId = $insertedRecord ? intval($insertedRecord['order_id']) : 0;


    if (!$localOrderId) {
        throw new Exception('保存本地订单记录成功但无法获取订单ID');
    }

    return $localOrderId;
}

/**
 * 转发订单到上游
 */
function createOrderWithUpstream($config) {
    global $userrow;

    validateUser();

    // 获取订单数据
    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data) {
        $data = $_POST;
    }

    // 转换数据格式以匹配上游API
    if (isset($data['company_id'])) {
        $data['cid'] = $data['company_id'];  // 上游可能期望 cid 字段
    }

    // 确保必要字段存在
    if (!isset($data['action'])) {
        $data['action'] = 'create_order';
    }

    // 记录开始时间用于性能监控
    $startTime = microtime(true);

    // 转发到上游
    $baseUrl = rtrim($config['upstream_url'], '/');
    $upstreamUrl = $baseUrl . '/apitaowa.php?action=create_order&uid=' . $config['upstream_uid'] . '&key=' . $config['upstream_key'];

    // 准备请求数据

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $upstreamUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'User-Agent: SXGZ-Plugin'
    ]);

    $startTime = microtime(true);
    $response = curl_exec($ch);
    $endTime = microtime(true);

    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $curlInfo = curl_getinfo($ch);
    curl_close($ch);

    // 检查请求结果

    if ($error) {
        echo json_encode([
            'success' => false,
            'message' => '网络连接失败',
            'code' => 500
        ]);
        return;
    }

    if ($httpCode !== 200) {
        // 如果是400错误，可能是业务逻辑错误，直接返回上游的错误信息
        if ($httpCode === 400 && $response) {
            $upstreamResult = json_decode($response, true);
            if ($upstreamResult && isset($upstreamResult['message'])) {
                echo json_encode([
                    'success' => false,
                    'message' => '上游错误: ' . $upstreamResult['message'],
                    'code' => 400
                ]);
                return;
            }
        }

        echo json_encode([
            'success' => false,
            'message' => '上游服务器连接失败',
            'code' => 500
        ]);
        return;
    }

    $result = json_decode($response, true);
    if (!$result) {
        echo json_encode([
            'success' => false,
            'message' => '上游响应格式错误',
            'code' => 500
        ]);
        return;
    }

    // 如果上游创建成功，在本地也保存一份记录
    if (isset($result['success']) && $result['success'] && isset($result['data']['order_id'])) {
        try {
            $localOrderId = saveLocalOrderRecord($data, $result['data'], $userrow);
            if ($localOrderId) {
                // 将本地订单ID添加到返回结果中，供文件上传使用
                $result['data']['local_order_id'] = $localOrderId;
            }

            // 如果订单创建时就有文件信息，立即传递文件URL到上游
            if (!empty($data['uploaded_file']) || !empty($data['file_url'])) {
                try {
                    updateUpstreamOrderWithFile($config, $result['data']['order_id'], $localOrderId);
                } catch (Exception $e) {
                    // 文件传递失败不影响主流程
                }
            }
        } catch (Exception $e) {
            // 本地记录保存失败不影响主流程
        }
    }

    // 直接返回上游结果
    echo json_encode($result);
}

/**
 * 传递文件URL到上游
 */
function updateUpstreamOrderWithFile($config, $upstreamOrderId, $localOrderId) {
    global $DB;

    // 获取本地订单的文件信息
    $localOrder = $DB->get_row("SELECT * FROM fd_sxgz_orders WHERE order_id = '{$localOrderId}'");
    if (!$localOrder || empty($localOrder['uploaded_file'])) {
        return false;
    }

    // 构建完整的文件下载URL，包含token以便源台访问
    $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];

    // 生成token用于源台访问验证
    $token = md5($localOrderId . $config['upstream_key'] . date('Y-m-d'));

    // 构建包含token的文件URL
    $fileUrl = $baseUrl . $localOrder['uploaded_file'];
    // 检查URL是否已经包含token参数
    if (strpos($fileUrl, 'token=') === false) {
        if (strpos($fileUrl, '?') !== false) {
            $fileUrl .= '&token=' . $token;
        } else {
            $fileUrl .= '?token=' . $token;
        }
    }

    // 准备传递给上游的数据
    $fileData = [
        'action' => 'update_order_file',
        'order_no' => $localOrder['order_no'],  // 使用订单号而不是订单ID
        'file_url' => $fileUrl,
        'original_filename' => $localOrder['original_filename'],
        'file_size' => $localOrder['file_size'],
        'plugin_domain' => $_SERVER['HTTP_HOST']
    ];

    // 发送到上游
    $baseUpstreamUrl = rtrim($config['upstream_url'], '/');
    $upstreamUrl = $baseUpstreamUrl . '/apitaowa.php?action=update_order_file&uid=' . $config['upstream_uid'] . '&key=' . $config['upstream_key'];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $upstreamUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fileData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'User-Agent: SXGZ-Plugin-FileSync'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200) {
        $result = json_decode($response, true);
        return isset($result['success']) && $result['success'];
    }

    return false;
}



/**
 * 文件上传
 */
function uploadFile() {
    global $DB, $userrow;

    // 验证用户登录
    if (!isset($userrow) || !$userrow) {
        echo json_encode([
            'code' => 0,
            'msg' => '用户未登录'
        ]);
        return;
    }

    if (!isset($_FILES['file'])) {
        echo json_encode([
            'code' => 0,
            'msg' => '没有上传文件'
        ]);
        return;
    }

    $file = $_FILES['file'];
    $orderId = $_POST['order_id'] ?? '';
    $isReplacement = isset($_POST['is_replacement']) && $_POST['is_replacement'] === 'true';

    // 基本验证
    if ($file['error'] !== UPLOAD_ERR_OK) {
        echo json_encode([
            'code' => 0,
            'msg' => '文件上传错误: ' . $file['error']
        ]);
        return;
    }

    // 文件类型验证
    $allowedTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'zip', 'rar', '7z'];
    $fileExt = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($fileExt, $allowedTypes)) {
        echo json_encode([
            'code' => 0,
            'msg' => '不支持的文件类型'
        ]);
        return;
    }

    // 文件大小验证（10MB）
    if ($file['size'] > 10 * 1024 * 1024) {
        echo json_encode([
            'code' => 0,
            'msg' => '文件大小不能超过 10MB'
        ]);
        return;
    }

    // 如果提供了订单ID，验证订单
    if (!empty($orderId)) {
        // 记录调试信息

        // 尝试通过本地order_id或admin_notes中的上游订单ID查找
        $sql = "SELECT * FROM fd_sxgz_orders WHERE
            (order_id = '{$orderId}' OR admin_notes LIKE '%上游订单ID: {$orderId}%')
            AND uid = {$userrow['uid']}";


        $order = $DB->get_row($sql);

        if (!$order) {
            // 记录所有该用户的订单用于调试
            $allOrders = $DB->get_results("SELECT order_id, admin_notes FROM fd_sxgz_orders WHERE uid = {$userrow['uid']} ORDER BY order_id DESC LIMIT 5");

            echo json_encode([
                'code' => 0,
                'msg' => '订单不存在或无权限，订单ID: ' . $orderId . '。请检查订单是否创建成功。'
            ]);
            return;
        }


        // 有订单ID的情况 - 使用简单的目录结构
        $uploadDir = __DIR__ . '/uploads/uid_' . $userrow['uid'] . '/orderid_' . $orderId . '/';

        // 创建用户和订单专用目录
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0777, true)) {
                echo json_encode([
                    'code' => 0,
                    'msg' => '创建上传目录失败: ' . $uploadDir
                ]);
                return;
            }
        }

        $newFileName = date('YmdHis') . '_' . uniqid() . '.' . $fileExt;
        $uploadPath = $uploadDir . $newFileName;

        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            // 生成包含token的下载URL
            $upstreamConfigFile = getUpstreamConfigPath();
            $token = '';
            if ($upstreamConfigFile) {
                $upstreamConfig = include($upstreamConfigFile);
                if (!empty($upstreamConfig['upstream_key'])) {
                    $token = md5($orderId . $upstreamConfig['upstream_key'] . date('Y-m-d'));
                }
            }

            $downloadUrl = '/sxgz/api.php?action=download_file&uid=' . $userrow['uid'] . '&order_id=' . $orderId . '&file=' . $newFileName;
            if (!empty($token)) {
                $downloadUrl .= '&token=' . $token;
            }

            $originalName = $DB->escape($file['name']);
            $fileSize = intval($file['size']);

            $updateSql = "UPDATE fd_sxgz_orders SET
                            uploaded_file = '{$downloadUrl}',
                            original_filename = '{$originalName}',
                            file_size = {$fileSize},
                            updated_at = NOW()
                          WHERE order_id = '{$orderId}'";

            $DB->query($updateSql);

            // 文件上传成功后，立即传递文件URL到上游
            try {
                $upstreamConfigFile = getUpstreamConfigPath();
                if ($upstreamConfigFile) {
                    $upstreamConfig = include($upstreamConfigFile);
                    if (!empty($upstreamConfig['upstream_url']) && !empty($upstreamConfig['upstream_uid']) && !empty($upstreamConfig['upstream_key'])) {
                        // 获取订单信息，查找上游订单ID
                        $localOrder = $DB->get_row("SELECT * FROM fd_sxgz_orders WHERE order_id = '{$orderId}'");
                        if ($localOrder) {
                            // 直接从agent_order_id字段获取上游订单ID
                            $upstreamOrderId = intval($localOrder['agent_order_id']);
                            if ($upstreamOrderId > 0) {
                                updateUpstreamOrderWithFile($upstreamConfig, $upstreamOrderId, $orderId);
                            } else {
                            }
                        }
                    }
                }
            } catch (Exception $e) {
                // 文件URL传递失败不影响主流程，只记录日志
            }

            echo json_encode([
                'code' => 1,
                'msg' => $isReplacement ? '文件替换成功' : '文件上传成功',
                'downurl' => $downloadUrl,
                'data' => [
                    'filename' => $newFileName,
                    'original_name' => $file['name'],
                    'size' => $file['size'],
                    'download_url' => $downloadUrl
                ]
            ]);
        } else {
            echo json_encode([
                'code' => 0,
                'msg' => '文件保存失败'
            ]);
        }
    } else {
        // 临时上传（无订单关联）
        $allowedExts = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'zip', 'rar', '7z'];
        $fileExt = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

        if (!in_array($fileExt, $allowedExts)) {
            echo json_encode([
                'code' => 0,
                'msg' => '不支持的文件类型，只支持：' . implode(', ', $allowedExts)
            ]);
            return;
        }

        if ($file['size'] > 10 * 1024 * 1024) {
            echo json_encode([
                'code' => 0,
                'msg' => '文件大小不能超过10MB'
            ]);
            return;
        }

        // 临时文件存储 - 使用简单的相对路径
        $uploadDir = __DIR__ . '/uploads/temp/';

        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0777, true)) {
                echo json_encode([
                    'code' => 0,
                    'msg' => '创建临时上传目录失败: ' . $uploadDir
                ]);
                return;
            }
        }

        $newFileName = date('YmdHis') . '_' . uniqid() . '.' . $fileExt;
        $uploadPath = $uploadDir . $newFileName;

        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            echo json_encode([
                'code' => 1,
                'msg' => '文件上传成功',
                'downurl' => '/sxgz/uploads/temp/' . $newFileName,
                'data' => [
                    'filename' => $newFileName,
                    'original_name' => $file['name'],
                    'size' => $file['size']
                ]
            ]);
        } else {
            echo json_encode([
                'code' => 0,
                'msg' => '保存文件失败'
            ]);
        }
    }
}

/**
 * 获取订单列表
 */
function getOrders() {
    global $DB, $userrow;

    validateUser();

    // 测试模式：如果没有用户信息，使用默认值
    if (!isset($userrow) || !$userrow) {
        $userrow = [
            'uid' => 1,
            'user' => 'test_user',
            'name' => '测试用户'
        ];
    }

    // 检查是否配置了上游
    $upstreamConfigFile = getUpstreamConfigPath();
    if ($upstreamConfigFile) {
        $upstreamConfig = include($upstreamConfigFile);
        if (!empty($upstreamConfig['upstream_url']) && !empty($upstreamConfig['upstream_uid']) && !empty($upstreamConfig['upstream_key'])) {
            // 获取混合订单（本地 + 上游同步）
            getMixedOrders($upstreamConfig);
            return;
        }
    }

    // 获取本地订单（原有逻辑）
    getOrdersLocal();
}

/**
 * 获取混合订单（本地 + 上游同步）
 */
function getMixedOrders($config) {
    global $DB, $userrow;

    $page = $_GET['page'] ?? 1;
    $limit = 20;
    $offset = ($page - 1) * $limit;

    // 首先获取本地订单
    $localOrders = [];
    try {
        $tableExistsResult = $DB->get_row("SHOW TABLES LIKE 'fd_sxgz_orders'");
        if (!empty($tableExistsResult)) {
            $localOrdersResult = $DB->get_results("
                SELECT * FROM fd_sxgz_orders
                WHERE uid = '{$userrow['uid']}'
                ORDER BY created_at DESC
                LIMIT {$limit} OFFSET {$offset}
            ");

            if ($localOrdersResult) {
                foreach ($localOrdersResult as $order) {
                    $localOrders[] = [
                        'id' => $order['order_id'],
                        'order_id' => $order['order_id'],
                        'order_no' => $order['order_no'],
                        'status' => $order['status'],
                        'created_at' => $order['created_at'],
                        'uid' => $order['uid'],
                        'username' => '', // 代理商模式下不显示用户名
                        'user_realname' => '', // 代理商模式下不显示真实姓名
                        'customer_name' => $order['customer_name'] ?? '',
                        'customer_phone' => $order['customer_phone'] ?? '',
                        'customer_email' => $order['customer_email'] ?? '',
                        'customer_address' => $order['customer_address'] ?? '',
                        'company_name' => $order['company_name'] ?? '',
                        'total_price' => $order['total_price'] ?? '0.00',
                        'base_price' => $order['base_price'] ?? '0.00',
                        'print_price' => $order['print_price'] ?? '0.00',
                        'license_price' => $order['license_price'] ?? '0.00',
                        'service_type' => $order['service_type'] ?? 'electronic',
                        'material_type' => $order['material_type'] ?? 'upload',
                        'business_license' => $order['business_license'] ?? 0,
                        'only_business_license' => $order['only_business_license'] ?? 0,
                        'print_copies' => $order['print_copies'] ?? 1,
                        'print_options' => $order['print_options'] ?? '',
                        'courier_company' => $order['courier_company'] ?? '',
                        'tracking_number' => $order['tracking_number'] ?? '',
                        'original_filename' => $order['original_filename'] ?? '',
                        'file_size' => $order['file_size'] ?? 0,
                        'admin_notes' => $order['admin_notes'] ?? '',
                        'special_requirements' => $order['special_requirements'] ?? '',
                        'uploaded_file' => $order['uploaded_file'] ?? '',
                        'source' => 'local'
                    ];
                }
            }
        }
    } catch (Exception $e) {
        // 本地订单获取失败，继续尝试上游
    }

    // 如果本地有订单，直接返回本地订单
    if (!empty($localOrders)) {
        echo json_encode([
            'success' => true,
            'data' => [
                'orders' => $localOrders,
                'total' => count($localOrders),
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil(count($localOrders) / $limit)
            ],
            'source' => 'local_mixed'
        ]);
        return;
    }

    // 如果本地没有订单，尝试从上游获取
    getOrdersFromUpstream($config);
}

/**
 * 从上游获取订单
 */
function getOrdersFromUpstream($config) {
    global $userrow;

    $page = $_GET['page'] ?? 1;
    $baseUrl = rtrim($config['upstream_url'], '/');
    $upstreamUrl = $baseUrl . '/apitaowa.php?action=sync_orders&uid=' . $config['upstream_uid'] . '&key=' . $config['upstream_key'] . '&page=' . $page;

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $upstreamUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'User-Agent: SXGZ-Plugin'
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200 && $response) {
        echo $response;
    } else {
        echo json_encode([
            'success' => false,
            'message' => '无法连接上游服务器'
        ]);
    }
}

/**
 * 获取本地订单（原有逻辑）
 */
function getOrdersLocal() {
    global $DB, $userrow;

    // 检查表是否存在
    $tableExistsResult = $DB->get_row("SHOW TABLES LIKE 'fd_sxgz_orders'");
    $tableExists = !empty($tableExistsResult);

    if (!$tableExists) {
        echo json_encode([
            'success' => false,
            'message' => '订单表不存在',
            'data' => [
                'orders' => [],
                'total' => 0,
                'page' => 1,
                'pages' => 0
            ]
        ]);
        return;
    }

    // 过滤输入参数
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 15);
    $offset = ($page - 1) * $limit;

    // 管理员可以查看所有订单，普通用户只能查看自己的订单
    if ($userrow['uid'] == 1) {
        $where = "WHERE 1=1"; // 管理员查看所有订单
        $tablePrefix = "o."; // 管理员查询使用表别名
    } else {
        $where = "WHERE uid = {$userrow['uid']}"; // 普通用户只查看自己的订单
        $tablePrefix = ""; // 普通用户查询不使用表别名
    }

    // 搜索条件
    if (!empty($_GET['search'])) {
        $search = $DB->escape(trim(strip_tags($_GET['search'])));
        $searchField = isset($_GET['search_field']) ? $DB->escape(trim(strip_tags($_GET['search_field']))) : '';

        // 调试信息（可以在生产环境中删除）

        if (!empty($searchField)) {
            // 根据指定字段搜索
            switch ($searchField) {
                case 'uid':
                    if ($userrow['uid'] === 1) { // 只有管理员可以按UID搜索
                        $where .= " AND {$tablePrefix}uid = '{$search}'";
                    }
                    break;
                case 'username':
                    if ($userrow['uid'] === 1) { // 只有管理员可以按用户名搜索
                        // 由于管理员查询时已经JOIN了用户表，可以直接搜索
                        $where .= " AND (u.user LIKE '%{$search}%' OR u.name LIKE '%{$search}%')";
                    }
                    break;
                case 'oid':
                    if ($userrow['uid'] === 1) { // 只有管理员可以按订单ID搜索
                        $where .= " AND {$tablePrefix}order_id LIKE '%{$search}%'";
                    }
                    break;
                case 'customer_name':
                    $where .= " AND {$tablePrefix}customer_name LIKE '%{$search}%'";
                    break;
                case 'company_name':
                    $where .= " AND {$tablePrefix}company_name LIKE '%{$search}%'";
                    break;
                case 'order_no':
                    $where .= " AND {$tablePrefix}order_no LIKE '%{$search}%'";
                    break;
                case 'special_requirements':
                    $where .= " AND {$tablePrefix}special_requirements LIKE '%{$search}%'";
                    break;
                case 'admin_notes':
                    $where .= " AND {$tablePrefix}admin_notes LIKE '%{$search}%'";
                    break;
                default:
                    // 默认全字段搜索 - 包含所有可搜索字段
                    if ($userrow['uid'] === 1) {
                        // 管理员可以搜索用户信息
                        $where .= " AND ({$tablePrefix}order_id LIKE '%{$search}%' OR {$tablePrefix}order_no LIKE '%{$search}%' OR {$tablePrefix}customer_name LIKE '%{$search}%' OR {$tablePrefix}company_name LIKE '%{$search}%' OR {$tablePrefix}special_requirements LIKE '%{$search}%' OR {$tablePrefix}admin_notes LIKE '%{$search}%' OR u.user LIKE '%{$search}%' OR u.name LIKE '%{$search}%')";
                    } else {
                        $where .= " AND ({$tablePrefix}order_id LIKE '%{$search}%' OR {$tablePrefix}order_no LIKE '%{$search}%' OR {$tablePrefix}customer_name LIKE '%{$search}%' OR {$tablePrefix}company_name LIKE '%{$search}%' OR {$tablePrefix}special_requirements LIKE '%{$search}%' OR {$tablePrefix}admin_notes LIKE '%{$search}%')";
                    }
                    break;
            }
        } else {
            // 全字段搜索 - 包含所有可搜索字段
            if ($userrow['uid'] === 1) {
                // 管理员可以搜索用户信息
                $where .= " AND ({$tablePrefix}order_id LIKE '%{$search}%' OR {$tablePrefix}order_no LIKE '%{$search}%' OR {$tablePrefix}customer_name LIKE '%{$search}%' OR {$tablePrefix}company_name LIKE '%{$search}%' OR {$tablePrefix}special_requirements LIKE '%{$search}%' OR {$tablePrefix}admin_notes LIKE '%{$search}%' OR u.user LIKE '%{$search}%' OR u.name LIKE '%{$search}%')";
            } else {
                $where .= " AND ({$tablePrefix}order_id LIKE '%{$search}%' OR {$tablePrefix}order_no LIKE '%{$search}%' OR {$tablePrefix}customer_name LIKE '%{$search}%' OR {$tablePrefix}company_name LIKE '%{$search}%' OR {$tablePrefix}special_requirements LIKE '%{$search}%' OR {$tablePrefix}admin_notes LIKE '%{$search}%')";
            }
        }
    }

    if (!empty($_GET['status'])) {
        $status = $DB->escape(trim(strip_tags($_GET['status'])));
        $where .= " AND {$tablePrefix}status = '{$status}'";
    }

    // 调试信息 - 输出最终的WHERE条件

    // 根据用户权限构建不同的查询
    if ($userrow['uid'] == 1) {
        // 管理员查询 - 包含用户信息
        $countSql = "SELECT COUNT(*) FROM fd_sxgz_orders o {$where}";
        $total = $DB->count($countSql);

        $orderSql = "SELECT o.*, u.user as username, u.name as user_realname,
                     COALESCE(o.processed_file_url, '') as processed_file_url
                     FROM fd_sxgz_orders o
                     LEFT JOIN qingka_wangke_user u ON o.uid = u.uid
                     {$where} ORDER BY
                     CASE
                         WHEN o.status = 'processing' THEN 1
                         WHEN o.status = 'refund_requested' THEN 2
                         WHEN o.status = 'pending' THEN 3
                         ELSE 4
                     END ASC,
                     o.created_at DESC
                     LIMIT {$offset}, {$limit}";
    } else {
        // 普通用户查询 - 不包含用户信息
        $countSql = "SELECT COUNT(*) FROM fd_sxgz_orders {$where}";
        $total = $DB->count($countSql);

        $orderSql = "SELECT *, COALESCE(processed_file_url, '') as processed_file_url
                     FROM fd_sxgz_orders {$where} ORDER BY
                     CASE
                         WHEN status = 'processing' THEN 1
                         WHEN status = 'refund_requested' THEN 2
                         WHEN status = 'pending' THEN 3
                         ELSE 4
                     END ASC,
                     created_at DESC
                     LIMIT {$offset}, {$limit}";
    }

    $orders = $DB->get_results($orderSql);

    // 处理订单数据，确保包含所有必要字段
    foreach ($orders as &$order) {
        // 确保所有字段都有默认值
        $order['username'] = $order['username'] ?? '';
        $order['user_realname'] = $order['user_realname'] ?? '';
        $order['customer_phone'] = $order['customer_phone'] ?? '';
        $order['customer_email'] = $order['customer_email'] ?? '';
        $order['customer_address'] = $order['customer_address'] ?? '';
        $order['tracking_number'] = $order['tracking_number'] ?? '';
        $order['courier_company'] = $order['courier_company'] ?? '';
        $order['admin_notes'] = $order['admin_notes'] ?? '';
        $order['special_requirements'] = $order['special_requirements'] ?? '';
        $order['uploaded_file'] = $order['uploaded_file'] ?? '';
        $order['original_filename'] = $order['original_filename'] ?? '';
        $order['file_size'] = $order['file_size'] ?? '';

        // 处理后文件URL字段（如果数据库中没有这个字段，手动添加）
        if (!isset($order['processed_file_url'])) {
            $order['processed_file_url'] = '';
        }

        // 不再添加测试URL，使用真实的处理后文件URL

        // 添加来源标识
        $order['source'] = 'local';
    }

    // 调试信息 - 输出查询结果

    echo json_encode([
        'success' => true,
        'data' => [
            'orders' => $orders,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

/**
 * 获取单个订单详情
 */
function getOrder() {
    global $DB, $userrow;

    // 初始化文件管理器
    require_once('file_manager.php');
    $fileManager = new SxgzFileManager();

    $orderId = $_GET['order_id'] ?? '';
    if (empty($orderId)) {
        throw new Exception('缺少订单ID', 400);
    }

    $where = "WHERE order_id = '{$orderId}'";

    // 非管理员只能查看自己的订单
    if (!isset($userrow) || $userrow['uid'] != 1) {
        if (!isset($userrow) || !$userrow) {
            throw new Exception('用户未登录', 401);
        }
        $where .= " AND uid = {$userrow['uid']}";
    }

    $order = $DB->get_row("SELECT * FROM fd_sxgz_orders {$where}");

    if (!$order) {
        throw new Exception('订单不存在', 404);
    }

    // 获取文件列表
    $files = $fileManager->getOrderFiles($orderId);

    // 获取状态日志
    $logs = $DB->get_results("SELECT * FROM FD_sxgz_order_logs WHERE order_id = '{$orderId}' ORDER BY created_at DESC");

    echo json_encode([
        'success' => true,
        'data' => [
            'order' => $order,
            'files' => $files,
            'logs' => $logs
        ]
    ]);
}

/**
 * 更新订单
 */
function updateOrder() {
    global $DB, $userrow;

    validateUser();

    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data) {
        $data = $_POST;
    }

    // 过滤输入参数
    $orderId = isset($data['order_id']) ? $DB->escape(trim(strip_tags($data['order_id']))) : '';
    if (empty($orderId)) {
        throw new Exception('缺少订单ID', 400);
    }

    // 验证订单存在且属于当前用户
    $order = $DB->get_row("SELECT * FROM fd_sxgz_orders WHERE order_id = '{$orderId}' AND uid = {$userrow['uid']}");
    if (!$order) {
        throw new Exception('订单不存在', 404);
    }

    // 只允许更新pending状态的订单
    if ($order['status'] !== 'pending') {
        throw new Exception('订单状态不允许修改', 400);
    }

    // 准备更新数据
    $updateFields = [];
    $allowedFields = ['customer_name', 'customer_email', 'customer_phone', 'customer_address', 'special_requirements'];

    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            // 过滤输入值 - 使用 $DB->escape() 提供更强安全保护
            $value = $DB->escape(trim(strip_tags($data[$field])));
            $updateFields[] = "{$field} = '{$value}'";
        }
    }

    if (empty($updateFields)) {
        throw new Exception('没有可更新的字段', 400);
    }

    $updateFields[] = "updated_at = NOW()";
    $updateSql = "UPDATE fd_sxgz_orders SET " . implode(', ', $updateFields) . " WHERE order_id = '{$orderId}'";

    if ($DB->query($updateSql)) {
        echo json_encode([
            'success' => true,
            'message' => '订单更新成功'
        ]);
    } else {
        throw new Exception('订单更新失败', 500);
    }
}


/**
 * 获取公司列表（优先从上游获取）
 */
function getCompanies() {
    global $DB;

    // 测试模式：跳过用户验证
    // validateUser();

    $cacheFile = getCacheFilePath('companies_cache.json');
    $cacheTime = 3600; // 缓存1小时


    // 第一优先级：尝试从上游获取最新数据
    try {
        $companies = fetchCompaniesFromUpstream();

        // 保存到缓存（使用统一的数据结构）
        $cacheData = [
            'timestamp' => time(),
            'data' => $companies
        ];
        $cacheWriteResult = file_put_contents($cacheFile, json_encode($cacheData, JSON_UNESCAPED_UNICODE));

        echo json_encode([
            'success' => true,
            'data' => $companies,
            'from_cache' => false,
            'cache_time' => date('Y-m-d H:i:s'),
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
    }

    // 第二优先级：检查有效缓存
    if (file_exists($cacheFile)) {
        $cacheData = json_decode(file_get_contents($cacheFile), true);
        if ($cacheData && isset($cacheData['timestamp'])) {
            $cacheAge = time() - $cacheData['timestamp'];

            // 如果缓存未过期，使用有效缓存
            if ($cacheAge < $cacheTime) {

                echo json_encode([
                    'success' => true,
                    'data' => $cacheData['data'] ?? $cacheData['companies'] ?? null,
                    'from_cache' => true,
                    'cache_time' => date('Y-m-d H:i:s', $cacheData['timestamp']),
                    'cache_age' => $cacheAge,
                    'warning' => '上游连接失败，使用有效缓存',
                ], JSON_UNESCAPED_UNICODE);
                return;
            }

            // 第三优先级：使用过期缓存作为降级方案
            $companies = $cacheData['data'] ?? $cacheData['companies'] ?? null;
            if ($companies) {

                echo json_encode([
                    'success' => true,
                    'data' => $companies,
                    'from_cache' => true,
                    'cache_expired' => true,
                    'cache_time' => date('Y-m-d H:i:s', $cacheData['timestamp']),
                    'cache_age' => $cacheAge,
                    'warning' => '无法连接上游，使用过期缓存数据',
                ], JSON_UNESCAPED_UNICODE);
                return;
            } else {
            }
        } else {
        }
    } else {
    }

    // 所有方法都失败了
    echo json_encode([
        'success' => false,
        'message' => '无法获取公司列表：上游连接失败，缓存无效。插件包必须连接上游才能使用。',
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 管理员获取订单列表
 */
function getAdminOrders() {
    global $DB, $userrow;

    // 验证管理员权限
    validateUser(true);

    // 过滤输入参数
    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 15);
    $offset = ($page - 1) * $limit;

    $where = "WHERE 1=1";

    // 搜索条件
    if (!empty($_GET['search'])) {
        $search = $DB->escape(trim(strip_tags($_GET['search'])));
        $searchField = isset($_GET['search_field']) ? $DB->escape(trim(strip_tags($_GET['search_field']))) : '';

        switch ($searchField) {
            case 'order_no':
                $where .= " AND order_no LIKE '%{$search}%'";
                break;
            case 'customer_name':
                $where .= " AND customer_name LIKE '%{$search}%'";
                break;
            case 'customer_email':
                $where .= " AND customer_email LIKE '%{$search}%'";
                break;
            default:
                // 管理员全字段搜索 - 包含所有可搜索字段
                $where .= " AND (order_id LIKE '%{$search}%' OR order_no LIKE '%{$search}%' OR customer_name LIKE '%{$search}%' OR customer_email LIKE '%{$search}%' OR company_name LIKE '%{$search}%')";
        }
    }

    if (!empty($_GET['status'])) {
        $status = $DB->escape(trim(strip_tags($_GET['status'])));
        $where .= " AND status = '{$status}'";
    }

    if (!empty($_GET['service_type'])) {
        $serviceType = $DB->escape(trim(strip_tags($_GET['service_type'])));
        $where .= " AND service_type = '{$serviceType}'";
    }

    if (!empty($_GET['date_from'])) {
        $dateFrom = $DB->escape($_GET['date_from']);
        $where .= " AND DATE(created_at) >= '{$dateFrom}'";
    }

    if (!empty($_GET['date_to'])) {
        $dateTo = $DB->escape($_GET['date_to']);
        $where .= " AND DATE(created_at) <= '{$dateTo}'";
    }

    // 获取总数
    $total = $DB->count("SELECT COUNT(*) FROM fd_sxgz_orders {$where}");

    // 获取订单列表 - 处理中>申请退款>待处理的订单顶置
    $orders = $DB->get_results("SELECT * FROM fd_sxgz_orders {$where} ORDER BY
        CASE
            WHEN status = 'processing' THEN 1
            WHEN status = 'refund_requested' THEN 2
            WHEN status = 'pending' THEN 3
            ELSE 4
        END ASC,
        created_at DESC
        LIMIT {$offset}, {$limit}");

    echo json_encode([
        'success' => true,
        'data' => [
            'orders' => $orders,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

/**
 * 管理员更新订单
 */
function adminUpdateOrder() {
    global $DB, $userrow;

    // 验证管理员权限
    validateUser(true);

    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data) {
        $data = $_POST;
    }

    // 过滤输入参数
    $orderId = isset($data['order_id']) ? $DB->escape(trim(strip_tags($data['order_id']))) : '';
    if (empty($orderId)) {
        throw new Exception('缺少订单ID', 400);
    }

    // 验证订单存在
    $order = $DB->get_row("SELECT * FROM fd_sxgz_orders WHERE order_id = '{$orderId}'");
    if (!$order) {
        throw new Exception('订单不存在', 404);
    }

    $oldStatus = $order['status'];
    $newStatus = isset($data['status']) ? $DB->escape(trim(strip_tags($data['status']))) : $oldStatus;
    $adminNotes = isset($data['admin_notes']) ? $DB->escape(trim(strip_tags($data['admin_notes']))) : '';

    // 准备更新数据
    $updateFields = [];

    if ($newStatus !== $oldStatus) {
        $updateFields[] = "status = '{$newStatus}'";

        if ($newStatus === 'completed') {
            $updateFields[] = "completed_at = NOW()";
        }
    }

    if (!empty($adminNotes)) {
        // $adminNotes 已经在前面用 $DB->escape() 处理过了，这里不需要重复转义
        $updateFields[] = "admin_notes = '{$adminNotes}'";
    }

    if (isset($data['processed_files'])) {
        // 过滤处理文件数据（确保是数组）
        $processedFiles = is_array($data['processed_files']) ? $data['processed_files'] : [];
        $processedFiles = json_encode($processedFiles);
        $updateFields[] = "processed_files = '{$processedFiles}'";
    }

    $updateFields[] = "updated_at = NOW()";

    // 更新订单
    $updateSql = "UPDATE fd_sxgz_orders SET " . implode(', ', $updateFields) . " WHERE order_id = '{$orderId}'";

    if ($DB->query($updateSql)) {
        // 记录状态变更日志
        if ($newStatus !== $oldStatus) {
            logStatusChange($orderId, $oldStatus, $newStatus, $userrow['uid'], $userrow['user'], $adminNotes);
        }

        echo json_encode([
            'success' => true,
            'message' => '订单更新成功'
        ]);
    } else {
        throw new Exception('订单更新失败', 500);
    }
}

/**
 * 计算订单价格
 */
function calculatePricing($data, $company) {
    global $userrow, $DB;

    // 获取用户费率
    $userRate = floatval($userrow['addprice'] ?? 1.0);
    if ($userRate <= 0) {
        $userRate = 1.0; // 默认费率
    }

    // 基础价格 = 公司价格 × 用户费率
    $basePrice = floatval($company['price']) * $userRate;
    $printPrice = 0;
    $licensePrice = 0;

    // 计算打印费用（超过10张每张0.5元，不受用户费率影响）
    $printCopies = intval($data['print_copies'] ?? 0);
    if ($printCopies > 10) {
        $printPrice = ($printCopies - 10) * 0.5;
    }

    // 计算营业执照费用（支持多个营业执照公司）
    if (!empty($data['business_license']) && empty($data['only_business_license'])) {
        // 获取选中的营业执照公司列表
        $selectedLicenseCompanies = $data['selected_license_companies'] ?? [];

        if (!empty($selectedLicenseCompanies) && is_array($selectedLicenseCompanies)) {
            $licensePrice = 0;
            foreach ($selectedLicenseCompanies as $licenseCid) {
                $licenseCompany = $DB->get_row("SELECT price FROM qingka_wangke_class WHERE cid = " . intval($licenseCid));
                if ($licenseCompany) {
                    $licensePrice += floatval($licenseCompany['price']) * $userRate;
                }
            }
        } else {
            // 兼容旧版本：如果没有选择具体公司，使用默认价格
            $licensePrice = 100 * $userRate;
        }
    }

    // 如果仅需要营业执照，使用营业执照专用价格（受用户费率影响）
    if (!empty($data['only_business_license'])) {
        $selectedLicenseCompanies = $data['selected_license_companies'] ?? [];

        if (!empty($selectedLicenseCompanies) && is_array($selectedLicenseCompanies)) {
            $basePrice = 0;
            foreach ($selectedLicenseCompanies as $licenseCid) {
                $licenseCompany = $DB->get_row("SELECT price FROM qingka_wangke_class WHERE cid = " . intval($licenseCid));
                if ($licenseCompany) {
                    $basePrice += floatval($licenseCompany['price']) * $userRate;
                }
            }
        } else {
            // 兼容旧版本：使用默认价格
            $basePrice = 100 * $userRate;
        }
        $licensePrice = 0;
        $printPrice = 0;
    }

    $totalPrice = $basePrice + $printPrice + $licensePrice;

    return [
        'base_price' => $basePrice,
        'print_price' => $printPrice,
        'license_price' => $licensePrice,
        'total_price' => $totalPrice,
        'user_rate' => $userRate
    ];
}

/**
 * 删除文件（管理员功能）
 */
function deleteFile() {
    global $DB, $userrow;

    // 初始化文件管理器
    require_once('file_manager.php');
    $fileManager = new SxgzFileManager();

    // 验证管理员权限
    validateUser(true);

    $data = json_decode(file_get_contents('php://input'), true);
    if (!$data) {
        $data = $_POST;
    }

    $orderId = $data['order_id'] ?? '';
    $filename = $data['filename'] ?? '';
    $fileType = $data['file_type'] ?? 'upload'; // upload 或 processed

    if (empty($orderId) || empty($filename)) {
        throw new Exception('缺少必要参数', 400);
    }

    // 验证订单存在
    $order = $DB->get_row("SELECT * FROM fd_sxgz_orders WHERE order_id = '{$orderId}'");
    if (!$order) {
        throw new Exception('订单不存在', 404);
    }

    $uid = $order['uid'];

    // 删除文件
    $result = $fileManager->deleteFile($uid, $orderId, $filename, $fileType);

    if ($result) {
        // 记录日志
        wlog($userrow['uid'], '删除文件', "订单: {$orderId}, 文件: {$filename}, 类型: {$fileType}", 0);

        echo json_encode([
            'success' => true,
            'message' => '文件删除成功'
        ]);
    } else {
        throw new Exception('文件删除失败', 500);
    }
}

/**
 * 用户申请退款
 */
function applyRefund() {
    global $DB, $userrow;

    // 验证用户登录
    validateUser();

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        // 过滤输入参数
        $orderId = isset($input['order_id']) ? $DB->escape(trim(strip_tags($input['order_id']))) : '';
        $reason = isset($input['reason']) ? $DB->escape(trim(strip_tags($input['reason']))) : '';

        if (empty($orderId)) {
            throw new Exception('订单ID不能为空');
        }

        if (empty($reason)) {
            throw new Exception('退款原因不能为空');
        }

        // 验证订单
        $order = $DB->get_row("SELECT * FROM fd_sxgz_orders WHERE order_id = '{$orderId}' AND uid = {$userrow['uid']}");
        if (!$order) {
            throw new Exception('订单不存在或无权限');
        }

        // 检查订单状态
        if (in_array($order['status'], ['cancelled', 'refunded', 'refund_requested'])) {
            $statusText = [
                'cancelled' => '已取消',
                'refunded' => '已退款',
                'refund_requested' => '已申请退款'
            ];
            throw new Exception('订单状态为' . $statusText[$order['status']] . '，无法申请退款');
        }

        // 更新订单状态和退款原因
        $result = $DB->query("UPDATE fd_sxgz_orders SET
                              status = 'refund_requested',
                              refund_reason = '{$DB->escape($reason)}',
                              updated_at = NOW()
                              WHERE order_id = '{$orderId}'");

        if ($result) {
            // 记录日志
            logStatusChange($orderId, $order['status'], 'refund_requested', $userrow['uid'], $userrow['user'], "用户申请退款：{$reason}");

            echo json_encode([
                'success' => true,
                'message' => '退款申请已提交，请等待管理员审核'
            ]);
        } else {
            throw new Exception('提交退款申请失败');
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * 管理员处理退款申请
 */
function processRefund() {
    global $DB, $userrow;

    // 验证管理员权限
    validateUser(true);

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        // 过滤输入参数
        $orderId = isset($input['order_id']) ? $DB->escape(trim(strip_tags($input['order_id']))) : '';
        $action = isset($input['action']) ? $DB->escape(trim(strip_tags($input['action']))) : ''; // approve 或 reject
        $adminNotes = isset($input['admin_notes']) ? $DB->escape(trim(strip_tags($input['admin_notes']))) : '';

        if (empty($orderId)) {
            throw new Exception('订单ID不能为空');
        }

        if (!in_array($action, ['approve', 'reject'])) {
            throw new Exception('无效的操作');
        }

        // 获取订单信息
        $order = $DB->get_row("SELECT * FROM fd_sxgz_orders WHERE order_id = '{$orderId}'");
        if (!$order) {
            throw new Exception('订单不存在');
        }

        if ($order['status'] !== 'refund_requested') {
            throw new Exception('订单未申请退款或已处理');
        }

        if ($action === 'approve') {
            // 批准退款
            $DB->query("UPDATE fd_sxgz_orders SET
                        status = 'refunded',
                        admin_notes = '{$DB->escape($adminNotes)}',
                        updated_at = NOW()
                        WHERE order_id = '{$orderId}'");

            // 退还用户余额
            $DB->query("UPDATE qingka_wangke_user SET money = money + {$order['total_price']} WHERE uid = {$order['uid']}");

            // 记录用户资金日志
            wlog($order['uid'], '实习盖章退款', "订单 {$orderId} 退款成功", $order['total_price']);

            // 记录订单状态变更
            logStatusChange($orderId, 'refund_requested', 'refunded', $userrow['uid'], $userrow['user'], "管理员批准退款：{$adminNotes}");

            $message = '退款申请已批准，金额已退还到用户余额';

        } else {
            // 拒绝退款，恢复到之前状态
            $previousStatus = 'pending'; // 默认恢复到待处理状态

            $DB->query("UPDATE fd_sxgz_orders SET
                        status = '{$previousStatus}',
                        admin_notes = '{$DB->escape($adminNotes)}',
                        updated_at = NOW()
                        WHERE order_id = '{$orderId}'");

            // 记录订单状态变更
            logStatusChange($orderId, 'refund_requested', $previousStatus, $userrow['uid'], $userrow['user'], "管理员拒绝退款：{$adminNotes}");

            $message = '退款申请已拒绝，订单恢复处理';
        }

        echo json_encode([
            'success' => true,
            'message' => $message
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * 管理员处理失败订单并自动退款
 */
function processFailedOrder() {
    global $DB, $userrow;

    // 验证管理员权限
    validateUser(true);

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        // 过滤输入参数
        $orderId = isset($input['order_id']) ? $DB->escape(trim(strip_tags($input['order_id']))) : '';

        if (empty($orderId)) {
            throw new Exception('订单ID不能为空');
        }

        // 获取订单信息
        $order = $DB->get_row("SELECT * FROM fd_sxgz_orders WHERE order_id = '{$orderId}'");
        if (!$order) {
            throw new Exception('订单不存在');
        }

        // 检查订单状态，只有处理中的订单才能标记为失败
        if ($order['status'] !== 'processing') {
            throw new Exception('只有处理中的订单才能标记为失败');
        }

        // 开始事务
        $DB->query("START TRANSACTION");

        try {
            // 更新订单状态为失败
            $adminNotes = '异常订单无法处理已退款！';
            $result = $DB->query("UPDATE fd_sxgz_orders SET
                                  status = 'failed',
                                  admin_notes = '{$DB->escape($adminNotes)}',
                                  updated_at = NOW()
                                  WHERE order_id = '{$orderId}'");

            if (!$result) {
                throw new Exception('更新订单状态失败');
            }

            // 退还用户余额
            $refundResult = $DB->query("UPDATE qingka_wangke_user SET money = money + {$order['total_price']} WHERE uid = {$order['uid']}");
            if (!$refundResult) {
                throw new Exception('退款失败');
            }

            // 记录用户资金日志
            wlog($order['uid'], '实习盖章处理失败退款', "订单 {$orderId} 处理失败自动退款", $order['total_price']);

            // 记录订单状态变更日志
            logStatusChange($orderId, 'processing', 'failed', $userrow['uid'], $userrow['user'], $adminNotes);

            // 提交事务
            $DB->query("COMMIT");

            echo json_encode([
                'success' => true,
                'message' => '订单已标记为处理失败，退款已完成'
            ]);

        } catch (Exception $e) {
            // 回滚事务
            $DB->query("ROLLBACK");
            throw $e;
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * 获取退款申请列表（管理员）
 */
function getRefundRequests() {
    global $DB, $userrow;

    // 验证管理员权限
    validateUser(true);

    try {
        // 过滤输入参数
        $page = intval($_GET['page'] ?? 1);
        $limit = intval($_GET['limit'] ?? 20);
        $status = isset($_GET['status']) ? $DB->escape(trim(strip_tags($_GET['status']))) : '';

        $offset = ($page - 1) * $limit;
        $where = "WHERE o.status IN ('refund_requested', 'refunded')";

        if (!empty($status)) {
            if ($status === 'pending') {
                $where = "WHERE o.status = 'refund_requested'";
            } elseif ($status === 'approved') {
                $where = "WHERE o.status = 'refunded'";
            }
        }

        // 获取总数
        $total = $DB->get_var("
            SELECT COUNT(*)
            FROM fd_sxgz_orders o
            {$where}
        ");

        // 获取退款申请列表
        $refunds = $DB->get_results("
            SELECT o.order_id, o.order_no, o.uid, o.customer_name, o.customer_email,
                   o.company_name, o.service_type, o.total_price as refund_amount,
                   o.refund_reason as reason, o.status, o.admin_notes,
                   o.created_at, o.updated_at, u.user as username
            FROM fd_sxgz_orders o
            LEFT JOIN qingka_wangke_user u ON o.uid = u.uid
            {$where}
            ORDER BY o.updated_at DESC
            LIMIT {$offset}, {$limit}
        ");

        echo json_encode([
            'success' => true,
            'data' => [
                'refunds' => $refunds ?: [],
                'total' => intval($total),
                'page' => $page,
                'limit' => $limit
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}





/**
 * 获取状态文本
 */
function getStatusText($status) {
    $statusMap = [
        'pending' => '待处理',
        'processing' => '处理中',
        'completed' => '已完成',
        'cancelled' => '已取消',
        'failed' => '失败',
        'refund_requested' => '申请退款',
        'refunded' => '已退款'
    ];

    return $statusMap[$status] ?? $status;
}

/**
 * 获取服务类型文本
 */
function getServiceTypeText($serviceType) {
    $typeMap = [
        'electronic' => '电子版',
        'mail' => '邮寄服务',
        'both' => '邮寄+电子版'
    ];

    return $typeMap[$serviceType] ?? $serviceType;
}

/**
 * 导出订单数据
 */
function exportOrders() {
    global $DB, $userrow;

    // 验证用户登录
    validateUser();

    // 检查是否为管理员
    $isAdmin = ($userrow['uid'] == 1);

    try {
        // 检查表是否存在
        $tableExistsResult = $DB->get_row("SHOW TABLES LIKE 'fd_sxgz_orders'");
        $tableExists = !empty($tableExistsResult);
        if (!$tableExists) {
            echo json_encode([
                'success' => false,
                'message' => '订单表不存在，请先安装数据库'
            ]);
            return;
        }

        // 获取筛选参数 - 过滤输入
        $exportType = isset($_GET['export_type']) ? $DB->escape(trim(strip_tags($_GET['export_type']))) : 'filtered';
        $orderIds = isset($_GET['order_ids']) ? $DB->escape(trim(strip_tags($_GET['order_ids']))) : '';
        $status = isset($_GET['status']) ? $DB->escape(trim(strip_tags($_GET['status']))) : '';
        $search = isset($_GET['search']) ? $DB->escape(trim(strip_tags($_GET['search']))) : '';
        $searchField = isset($_GET['search_field']) ? $DB->escape(trim(strip_tags($_GET['search_field']))) : '';
        $format = isset($_GET['format']) ? $DB->escape(trim(strip_tags($_GET['format']))) : 'csv'; // csv 或 excel

        // 构建查询条件
        $where = "WHERE 1=1";

        // 如果不是管理员，只能导出自己的订单
        if (!$isAdmin) {
            $where .= " AND o.uid = {$userrow['uid']}";
        }

        // 根据导出类型构建不同的查询条件
        if ($exportType === 'selected' && !empty($orderIds)) {
            // 导出选中订单
            $orderIdArray = explode(',', $orderIds);
            $orderIdArray = array_map('intval', $orderIdArray); // 确保都是整数
            $orderIdList = implode(',', $orderIdArray);
            $where .= " AND o.order_id IN ({$orderIdList})";
        } else if ($exportType === 'single') {
            // 导出单个订单
            $orderId = isset($_GET['order_id']) ? intval($_GET['order_id']) : 0;
            if ($orderId > 0) {
                $where .= " AND o.order_id = {$orderId}";
            } else {
                throw new Exception('订单ID无效');
            }
        } else if ($exportType === 'filtered') {
            // 导出筛选结果
            if (!empty($status)) {
                $where .= " AND o.status = '{$DB->escape($status)}'";
            }

            if (!empty($search)) {
                $search = $DB->escape($search);
                if (!empty($searchField)) {
                    switch ($searchField) {
                        case 'uid':
                            $where .= " AND o.uid = '{$search}'";
                            break;
                        case 'oid':
                            $where .= " AND o.order_id = '{$search}'";
                            break;
                        case 'customer_name':
                            $where .= " AND o.customer_name LIKE '%{$search}%'";
                            break;
                        case 'company_name':
                            $where .= " AND o.company_name LIKE '%{$search}%'";
                            break;
                        case 'order_no':
                            $where .= " AND o.order_no LIKE '%{$search}%'";
                            break;
                        default:
                            $where .= " AND (o.order_no LIKE '%{$search}%' OR o.customer_name LIKE '%{$search}%' OR o.company_name LIKE '%{$search}%')";
                            break;
                    }
                } else {
                    $where .= " AND (o.order_no LIKE '%{$search}%' OR o.customer_name LIKE '%{$search}%' OR o.company_name LIKE '%{$search}%')";
                }
            }
        }
        // 如果是 'all' 类型，不添加额外的筛选条件

        // 查询订单数据 - 根据用户权限决定查询字段
        if ($isAdmin) {
            // 管理员可以看到所有字段 - 使用简化字段来测试
            $sql = "
                SELECT
                    o.order_id,
                    o.order_no,
                    o.uid,
                    '' as username,
                    o.customer_name,
                    o.company_name,
                    o.service_type,
                    o.status,
                    o.created_at,
                    IFNULL(o.customer_email, '') as customer_email,
                    IFNULL(o.customer_phone, '') as customer_phone,
                    IFNULL(o.customer_address, '') as customer_address,
                    IFNULL(o.material_type, '') as material_type,
                    IFNULL(o.business_license, 0) as business_license,
                    IFNULL(o.only_business_license, 0) as only_business_license,
                    IFNULL(o.print_copies, 1) as print_copies,
                    IFNULL(o.print_options, '') as print_options,
                    IFNULL(o.special_requirements, '') as special_requirements,
                    IFNULL(o.courier_company, '') as courier_company,
                    IFNULL(o.tracking_number, '') as tracking_number,
                    IFNULL(o.service_price, 0) as service_price,
                    IFNULL(o.print_price, 0) as print_price,
                    IFNULL(o.license_price, 0) as license_price,
                    IFNULL(o.total_price, 0) as total_price,
                    IFNULL(o.refund_reason, '') as refund_reason,
                    IFNULL(o.admin_notes, '') as admin_notes,
                    '' as remarks,
                    IFNULL(o.updated_at, o.created_at) as updated_at
                FROM fd_sxgz_orders o
                {$where}
                ORDER BY
                    CASE
                        WHEN o.status = 'processing' THEN 1
                        WHEN o.status = 'refund_requested' THEN 2
                        WHEN o.status = 'pending' THEN 3
                        ELSE 4
                    END ASC,
                    o.created_at DESC
            ";
        } else {
            // 普通用户只能看到基本字段，不包含敏感信息
            // 使用简化的字段列表，逐步添加字段来测试
            $sql = "
                SELECT
                    o.order_no,
                    o.customer_name,
                    o.company_name,
                    o.service_type,
                    o.status,
                    o.created_at,
                    IFNULL(o.customer_email, '') as customer_email,
                    IFNULL(o.customer_phone, '') as customer_phone,
                    IFNULL(o.customer_address, '') as customer_address,
                    IFNULL(o.material_type, '') as material_type,
                    IFNULL(o.business_license, 0) as business_license,
                    IFNULL(o.only_business_license, 0) as only_business_license,
                    IFNULL(o.print_copies, 1) as print_copies,
                    IFNULL(o.print_options, '') as print_options,
                    IFNULL(o.special_requirements, '') as special_requirements,
                    IFNULL(o.courier_company, '') as courier_company,
                    IFNULL(o.tracking_number, '') as tracking_number,
                    IFNULL(o.base_price, 0) as base_price,
                    IFNULL(o.print_price, 0) as print_price,
                    IFNULL(o.license_price, 0) as license_price,
                    IFNULL(o.total_price, 0) as total_price,
                    IFNULL(o.refund_reason, '') as refund_reason,
                    IFNULL(o.admin_notes, '') as admin_notes,
                    '' as remarks,
                    IFNULL(o.updated_at, o.created_at) as updated_at
                FROM fd_sxgz_orders o
                {$where}
                ORDER BY
                    CASE
                        WHEN o.status = 'processing' THEN 1
                        WHEN o.status = 'refund_requested' THEN 2
                        WHEN o.status = 'pending' THEN 3
                        ELSE 4
                    END ASC,
                    o.created_at DESC
            ";
        }

        $orders = $DB->get_results($sql);

        if (empty($orders)) {
            // 检查是否有任何订单数据
            $totalOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders");
            if ($totalOrders == 0) {
                echo json_encode([
                    'success' => false,
                    'message' => '系统中暂无订单数据'
                ]);
            } else if (!$isAdmin) {
                $userOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders WHERE uid = {$userrow['uid']}");
                if ($userOrders == 0) {
                    echo json_encode([
                        'success' => false,
                        'message' => '您还没有任何订单数据'
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'message' => '没有找到符合筛选条件的订单数据'
                    ]);
                }
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '没有找到符合筛选条件的订单数据'
                ]);
            }
            return;
        }

        // 生成文件名 - 根据用户权限区分
        $filename = $isAdmin ? 'sxgz_orders_' . date('YmdHis') : 'my_sxgz_orders_' . date('YmdHis');

        if ($format === 'excel') {
            exportToExcel($orders, $filename);
        } else {
            exportToCSV($orders, $filename);
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '导出失败: ' . $e->getMessage()
        ]);
    }
}

/**
 * 导出为CSV格式
 */
function exportToCSV($orders, $filename) {
    global $userrow;

    // 设置HTTP头
    header('Content-Type: text/csv; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');

    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";

    // 打开输出流
    $output = fopen('php://output', 'w');

    // 检查是否为管理员
    $isAdmin = ($userrow['uid'] == 1);

    // CSV表头 - 根据用户权限决定
    if ($isAdmin) {
        $headers = [
            '订单ID',
            '订单号',
            '用户ID',
            '用户名',
            '客户姓名',
            '客户邮箱',
            '客户电话',
            '客户地址',
            '公司名称',
            '服务类型',
            '材料类型',
            '需要营业执照',
            '仅需营业执照',
            '打印份数',
            '打印选项',
            '特殊要求',
            '快递公司',
            '快递单号',
            '基础价格',
            '打印费用',
            '执照费用',
            '总价格',
            '订单状态',
            '退款原因',
            '管理员备注',
            '客户备注',
            '创建时间',
            '更新时间'
        ];
    } else {
        $headers = [
            '订单号',
            '客户姓名',
            '客户邮箱',
            '客户电话',
            '客户地址',
            '公司名称',
            '服务类型',
            '材料类型',
            '需要营业执照',
            '仅需营业执照',
            '打印份数',
            '打印选项',
            '特殊要求',
            '快递公司',
            '快递单号',
            '基础价格',
            '打印费用',
            '执照费用',
            '总价格',
            '订单状态',
            '退款原因',
            '管理员回复',
            '客户备注',
            '创建时间',
            '更新时间'
        ];
    }

    fputcsv($output, $headers);

    // 输出数据行 - 根据用户权限决定
    foreach ($orders as $order) {
        if ($isAdmin) {
            $row = [
                $order['order_id'],
                $order['order_no'],
                $order['uid'],
                $order['username'] ?? '',
                $order['customer_name'],
                $order['customer_email'] ?? '',
                $order['customer_phone'] ?? '',
                $order['customer_address'] ?? '',
                $order['company_name'],
                getServiceTypeText($order['service_type']),
                $order['material_type'] ?? '',
                $order['business_license'] ? '是' : '否',
                $order['only_business_license'] ? '是' : '否',
                $order['print_copies'],
                $order['print_options'] ?? '',
                $order['special_requirements'] ?? '',
                $order['courier_company'] ?? '',
                $order['tracking_number'] ?? '',
                $order['base_price'],
                $order['print_price'],
                $order['license_price'],
                $order['total_price'],
                getStatusText($order['status']),
                $order['refund_reason'] ?? '',
                $order['admin_notes'] ?? '',
                $order['remarks'] ?? '',
                $order['created_at'],
                $order['updated_at'] ?? ''
            ];
        } else {
            $row = [
                $order['order_no'],
                $order['customer_name'],
                $order['customer_email'] ?? '',
                $order['customer_phone'] ?? '',
                $order['customer_address'] ?? '',
                $order['company_name'],
                getServiceTypeText($order['service_type']),
                $order['material_type'] ?? '',
                $order['business_license'] ? '是' : '否',
                $order['only_business_license'] ? '是' : '否',
                $order['print_copies'],
                $order['print_options'] ?? '',
                $order['special_requirements'] ?? '',
                $order['courier_company'] ?? '',
                $order['tracking_number'] ?? '',
                $order['base_price'],
                $order['print_price'],
                $order['license_price'],
                $order['total_price'],
                getStatusText($order['status']),
                $order['refund_reason'] ?? '',
                $order['admin_notes'] ?? '',
                $order['remarks'] ?? '',
                $order['created_at'],
                $order['updated_at'] ?? ''
            ];
        }

        fputcsv($output, $row);
    }

    fclose($output);
    exit;
}

/**
 * 导出为Excel格式（使用HTML表格模拟）
 */
function exportToExcel($orders, $filename) {
    global $userrow;

    // 设置HTTP头
    header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');

    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";

    // 检查是否为管理员
    $isAdmin = ($userrow['uid'] == 1);

    // 开始HTML表格
    echo '<table border="1">';
    echo '<tr style="background-color: #f0f0f0; font-weight: bold;">';

    if ($isAdmin) {
        echo '<td>订单ID</td>';
        echo '<td>订单号</td>';
        echo '<td>用户ID</td>';
        echo '<td>用户名</td>';
        echo '<td>客户姓名</td>';
        echo '<td>客户邮箱</td>';
        echo '<td>客户电话</td>';
        echo '<td>客户地址</td>';
        echo '<td>公司名称</td>';
        echo '<td>服务类型</td>';
        echo '<td>材料类型</td>';
        echo '<td>需要营业执照</td>';
        echo '<td>仅需营业执照</td>';
        echo '<td>打印份数</td>';
        echo '<td>打印选项</td>';
        echo '<td>特殊要求</td>';
        echo '<td>快递公司</td>';
        echo '<td>快递单号</td>';
        echo '<td>基础价格</td>';
        echo '<td>打印费用</td>';
        echo '<td>执照费用</td>';
        echo '<td>总价格</td>';
        echo '<td>订单状态</td>';
        echo '<td>退款原因</td>';
        echo '<td>管理员备注</td>';
        echo '<td>客户备注</td>';
        echo '<td>创建时间</td>';
        echo '<td>更新时间</td>';
    } else {
        echo '<td>订单号</td>';
        echo '<td>客户姓名</td>';
        echo '<td>客户邮箱</td>';
        echo '<td>客户电话</td>';
        echo '<td>客户地址</td>';
        echo '<td>公司名称</td>';
        echo '<td>服务类型</td>';
        echo '<td>材料类型</td>';
        echo '<td>需要营业执照</td>';
        echo '<td>仅需营业执照</td>';
        echo '<td>打印份数</td>';
        echo '<td>打印选项</td>';
        echo '<td>特殊要求</td>';
        echo '<td>快递公司</td>';
        echo '<td>快递单号</td>';
        echo '<td>基础价格</td>';
        echo '<td>打印费用</td>';
        echo '<td>执照费用</td>';
        echo '<td>总价格</td>';
        echo '<td>订单状态</td>';
        echo '<td>退款原因</td>';
        echo '<td>管理员回复</td>';
        echo '<td>客户备注</td>';
        echo '<td>创建时间</td>';
        echo '<td>更新时间</td>';
    }

    echo '</tr>';

    // 输出数据行 - 根据用户权限决定
    foreach ($orders as $order) {
        echo '<tr>';

        if ($isAdmin) {
            echo '<td>' . htmlspecialchars($order['order_id']) . '</td>';
            echo '<td>' . htmlspecialchars($order['order_no']) . '</td>';
            echo '<td>' . htmlspecialchars($order['uid']) . '</td>';
            echo '<td>' . htmlspecialchars($order['username'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_name']) . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_email'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_phone'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_address'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['company_name']) . '</td>';
            echo '<td>' . htmlspecialchars(getServiceTypeText($order['service_type'])) . '</td>';
            echo '<td>' . htmlspecialchars($order['material_type'] ?? '') . '</td>';
            echo '<td>' . ($order['business_license'] ? '是' : '否') . '</td>';
            echo '<td>' . ($order['only_business_license'] ? '是' : '否') . '</td>';
            echo '<td>' . htmlspecialchars($order['print_copies']) . '</td>';
            echo '<td>' . htmlspecialchars($order['print_options'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['special_requirements'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['courier_company'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['tracking_number'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['base_price']) . '</td>';
            echo '<td>' . htmlspecialchars($order['print_price']) . '</td>';
            echo '<td>' . htmlspecialchars($order['license_price']) . '</td>';
            echo '<td>' . htmlspecialchars($order['total_price']) . '</td>';
            echo '<td>' . htmlspecialchars(getStatusText($order['status'])) . '</td>';
            echo '<td>' . htmlspecialchars($order['refund_reason'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['admin_notes'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['remarks'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['created_at']) . '</td>';
            echo '<td>' . htmlspecialchars($order['updated_at'] ?? '') . '</td>';
        } else {
            echo '<td>' . htmlspecialchars($order['order_no']) . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_name']) . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_email'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_phone'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['customer_address'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['company_name']) . '</td>';
            echo '<td>' . htmlspecialchars(getServiceTypeText($order['service_type'])) . '</td>';
            echo '<td>' . htmlspecialchars($order['material_type'] ?? '') . '</td>';
            echo '<td>' . ($order['business_license'] ? '是' : '否') . '</td>';
            echo '<td>' . ($order['only_business_license'] ? '是' : '否') . '</td>';
            echo '<td>' . htmlspecialchars($order['print_copies']) . '</td>';
            echo '<td>' . htmlspecialchars($order['print_options'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['special_requirements'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['courier_company'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['tracking_number'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['base_price']) . '</td>';
            echo '<td>' . htmlspecialchars($order['print_price']) . '</td>';
            echo '<td>' . htmlspecialchars($order['license_price']) . '</td>';
            echo '<td>' . htmlspecialchars($order['total_price']) . '</td>';
            echo '<td>' . htmlspecialchars(getStatusText($order['status'])) . '</td>';
            echo '<td>' . htmlspecialchars($order['refund_reason'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['admin_notes'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['remarks'] ?? '') . '</td>';
            echo '<td>' . htmlspecialchars($order['created_at']) . '</td>';
            echo '<td>' . htmlspecialchars($order['updated_at'] ?? '') . '</td>';
        }

        echo '</tr>';
    }

    echo '</table>';
    exit;
}

/**
 * 获取统计数据
 */
function getStatistics() {
    global $DB, $userrow;

    // 验证管理员权限
    if (!isset($userrow) || $userrow['uid'] != 1) {
        echo json_encode(['success' => false, 'message' => '权限不足']);
        return;
    }

    try {
        // 检查表是否存在
        $tableExistsResult = $DB->get_row("SHOW TABLES LIKE 'fd_sxgz_orders'");
        $tableExists = !empty($tableExistsResult);
        if (!$tableExists) {
            echo json_encode([
                'success' => true,
                'data' => [
                    'total_orders' => 0,
                    'processing_orders' => 0,
                    'pending_orders' => 0,
                    'completed_orders' => 0,
                    'refund_requests' => 0,
                    'today_orders' => 0,
                    'total_revenue' => 0,
                    'active_users' => 0,

                    // 保持向后兼容性
                    'todayOrders' => 0,
                    'todayIncome' => '0.00',
                    'pendingOrders' => 0,
                    'totalOrders' => 0
                ]
            ]);
            return;
        }

        $today = date('Y-m-d');

        // 总订单数
        $totalOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders") ?: 0;

        // 处理中订单数
        $processingOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders WHERE status = 'processing'") ?: 0;

        // 待处理订单数
        $pendingOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders WHERE status = 'pending'") ?: 0;

        // 已完成订单数
        $completedOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders WHERE status = 'completed'") ?: 0;

        // 申请退款订单数
        $refundRequests = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders WHERE status = 'refund_requested'") ?: 0;

        // 今日订单数
        $todayOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders WHERE DATE(created_at) = '{$today}'") ?: 0;

        // 总收入（排除已取消和已退款的订单）
        $totalRevenue = $DB->get_var("SELECT COALESCE(SUM(total_price), 0) FROM fd_sxgz_orders WHERE status NOT IN ('cancelled', 'refunded')") ?: 0;

        // 活跃用户数（最近30天有订单的用户）
        $activeUsers = $DB->get_var("SELECT COUNT(DISTINCT uid) FROM fd_sxgz_orders WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)") ?: 0;

        echo json_encode([
            'success' => true,
            'data' => [
                'total_orders' => intval($totalOrders),
                'processing_orders' => intval($processingOrders),
                'pending_orders' => intval($pendingOrders),
                'completed_orders' => intval($completedOrders),
                'refund_requests' => intval($refundRequests),
                'today_orders' => intval($todayOrders),
                'total_revenue' => floatval($totalRevenue),
                'active_users' => intval($activeUsers),

                // 保持向后兼容性
                'todayOrders' => intval($todayOrders),
                'todayIncome' => number_format(floatval($totalRevenue), 2),
                'pendingOrders' => intval($pendingOrders),
                'totalOrders' => intval($totalOrders)
            ]
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '获取统计数据失败: ' . $e->getMessage(),
            'error' => $e->getMessage(),
            'line' => $e->getLine(),
            'file' => basename($e->getFile())
        ]);
    }
}

/**
 * 获取管理员统计数据
 */
function getAdminStats() {
    global $DB, $userrow;

    // 验证管理员权限
    if (!isset($userrow) || $userrow['uid'] != 1) {
        echo json_encode(['success' => false, 'message' => '权限不足']);
        return;
    }

    try {
        // 检查表是否存在
        $tableExistsResult = $DB->get_row("SHOW TABLES LIKE 'fd_sxgz_orders'");
        $tableExists = !empty($tableExistsResult);
        if (!$tableExists) {
            echo json_encode([
                'success' => true,
                'data' => [
                    'overview' => [
                        'total_orders' => 0,
                        'pending_orders' => 0,
                        'processing_orders' => 0,
                        'completed_orders' => 0,
                        'delivered_orders' => 0,
                        'cancelled_orders' => 0,
                        'today_orders' => 0
                    ],
                    'revenue' => [
                        'total' => 0,
                        'today' => 0,
                        'month' => 0
                    ]
                ]
            ]);
            return;
        }

        // 基础统计
        $totalOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders") ?: 0;
        $pendingOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders WHERE status = 'pending'") ?: 0;
        $processingOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders WHERE status = 'processing'") ?: 0;
        $completedOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders WHERE status = 'completed'") ?: 0;
        $deliveredOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders WHERE status = 'delivered'") ?: 0;
        $cancelledOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders WHERE status = 'cancelled'") ?: 0;

        // 收入统计
        $totalRevenue = $DB->get_var("SELECT SUM(total_price) FROM fd_sxgz_orders WHERE status IN ('completed', 'delivered')") ?: 0;
        $todayRevenue = $DB->get_var("SELECT SUM(total_price) FROM fd_sxgz_orders WHERE status IN ('completed', 'delivered') AND DATE(created_at) = CURDATE()") ?: 0;
        $monthRevenue = $DB->get_var("SELECT SUM(total_price) FROM fd_sxgz_orders WHERE status IN ('completed', 'delivered') AND YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())") ?: 0;

        // 今日新订单
        $todayOrders = $DB->get_var("SELECT COUNT(*) FROM fd_sxgz_orders WHERE DATE(created_at) = CURDATE()") ?: 0;

        echo json_encode([
            'success' => true,
            'data' => [
                'overview' => [
                    'total_orders' => intval($totalOrders),
                    'pending_orders' => intval($pendingOrders),
                    'processing_orders' => intval($processingOrders),
                    'completed_orders' => intval($completedOrders),
                    'delivered_orders' => intval($deliveredOrders),
                    'cancelled_orders' => intval($cancelledOrders),
                    'today_orders' => intval($todayOrders)
                ],
                'revenue' => [
                    'total' => floatval($totalRevenue),
                    'today' => floatval($todayRevenue),
                    'month' => floatval($monthRevenue)
                ]
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '获取统计数据失败: ' . $e->getMessage(),
            'error' => $e->getMessage(),
            'line' => $e->getLine(),
            'file' => basename($e->getFile())
        ]);
    }
}

/**
 * 获取用户等级费率信息
 */
function getUserRates() {
    try {
        // 调用外部API获取用户等级费率
        $apiUrl = $_SERVER['DOCUMENT_ROOT'] . '/apisub.php?act=djlist';

        // 使用curl代替file_get_contents
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost/apisub.php?act=djlist');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($response === false || $httpCode !== 200) {
            throw new Exception('无法获取费率信息，HTTP状态码: ' . $httpCode);
        }

        $rateData = json_decode($response, true);

        if (!$rateData || $rateData['code'] != 1) {
            // 如果API调用失败，返回默认费率数据
            $defaultRates = [
                ['id' => '1', 'name' => '超级管理员', 'rate' => '0.200'],
                ['id' => '2', 'name' => '金牌代理', 'rate' => '0.300'],
                ['id' => '3', 'name' => '钻石代理', 'rate' => '0.400'],
                ['id' => '4', 'name' => '黄金代理', 'rate' => '0.500'],
                ['id' => '5', 'name' => '白银代理', 'rate' => '0.600'],
                ['id' => '6', 'name' => '普通代理', 'rate' => '0.800']
            ];

            echo json_encode([
                'success' => true,
                'data' => $defaultRates,
                'note' => '使用默认费率数据'
            ]);
            return;
        }

        echo json_encode([
            'success' => true,
            'data' => $rateData['data']
        ]);

    } catch (Exception $e) {
        // 返回默认费率数据作为备用
        $defaultRates = [
            ['id' => '1', 'name' => '超级管理员', 'rate' => '0.200'],
            ['id' => '2', 'name' => '金牌代理', 'rate' => '0.300'],
            ['id' => '3', 'name' => '钻石代理', 'rate' => '0.400'],
            ['id' => '4', 'name' => '黄金代理', 'rate' => '0.500'],
            ['id' => '5', 'name' => '白银代理', 'rate' => '0.600'],
            ['id' => '6', 'name' => '普通代理', 'rate' => '0.800']
        ];

        echo json_encode([
            'success' => true,
            'data' => $defaultRates,
            'error' => $e->getMessage(),
            'note' => '使用默认费率数据'
        ]);
    }
}

/**
 * 代理商获取公司列表（不检查本地用户登录）
 */
function getCompaniesForAgent() {
    global $DB;

    // 验证代理商认证参数
    $uid = $_GET['uid'] ?? '';
    $key = $_GET['key'] ?? '';

    if (empty($uid) || empty($key)) {
        echo json_encode([
            'success' => false,
            'message' => '缺少认证参数',
            'code' => 401
        ]);
        return;
    }

    // 验证代理商用户存在且key正确
    $user = $DB->get_row("SELECT * FROM qingka_wangke_user WHERE uid = '{$uid}' AND `key` = '{$key}' AND active = 1");

    if (!$user) {
        echo json_encode([
            'success' => false,
            'message' => '代理商认证失败',
            'code' => 401
        ]);
        return;
    }

    // 初始化调试信息
    $debugInfo = [
        'function' => 'getCompaniesForAgent',
        'start_time' => date('Y-m-d H:i:s'),
        'steps' => [],
        'agent_uid' => $uid
    ];

    $cacheFile = getCacheFilePath('companies_cache.json');
    $cacheTime = 3600; // 缓存1小时


    // 第一优先级：尝试从上游获取最新数据
    try {
        $companies = fetchCompaniesFromUpstream();
        if ($companies) {

            // 保存到缓存
            $cacheData = [
                'timestamp' => time(),
                'data' => $companies
            ];
            file_put_contents($cacheFile, json_encode($cacheData, JSON_UNESCAPED_UNICODE));

            echo json_encode([
                'success' => true,
                'data' => $companies,
                'from_cache' => false,
                'cache_time' => date('Y-m-d H:i:s'),
            ], JSON_UNESCAPED_UNICODE);
            return;
        }
    } catch (Exception $e) {
    }

    // 第二优先级：检查有效缓存
    if (file_exists($cacheFile)) {
        $cacheData = json_decode(file_get_contents($cacheFile), true);
        if ($cacheData && isset($cacheData['timestamp'])) {
            $cacheAge = time() - $cacheData['timestamp'];

            // 如果缓存未过期，使用有效缓存
            if ($cacheAge < $cacheTime) {

                echo json_encode([
                    'success' => true,
                    'data' => $cacheData['data'],
                    'from_cache' => true,
                    'cache_time' => date('Y-m-d H:i:s', $cacheData['timestamp']),
                    'cache_age' => $cacheAge,
                    'warning' => '上游连接失败，使用有效缓存',
                ], JSON_UNESCAPED_UNICODE);
                return;
            }

            // 第三优先级：使用过期缓存作为降级方案
            if (isset($cacheData['data'])) {

                echo json_encode([
                    'success' => true,
                    'data' => $cacheData['data'],
                    'from_cache' => true,
                    'cache_expired' => true,
                    'cache_time' => date('Y-m-d H:i:s', $cacheData['timestamp']),
                    'cache_age' => $cacheAge,
                    'warning' => '无法连接上游，使用过期缓存数据',
                ], JSON_UNESCAPED_UNICODE);
                return;
            }
        }
    }

    // 所有方法都失败了
    echo json_encode([
        'success' => false,
        'message' => '无法获取公司列表：上游连接失败，缓存无效。插件包必须连接上游才能使用。',
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 管理员获取公司列表（用于管理）
 */
function getCompaniesForAdmin() {
    global $DB, $userrow;

    // 验证管理员权限
    if (!isset($userrow) || $userrow['uid'] != 1) {
        echo json_encode(['success' => false, 'message' => '权限不足']);
        return;
    }

    try {
        // 过滤输入参数
        $page = intval($_GET['page'] ?? 1);
        $limit = intval($_GET['limit'] ?? 20);
        $search = isset($_GET['search']) ? $DB->escape(trim(strip_tags($_GET['search']))) : '';

        $offset = ($page - 1) * $limit;
        $where = "WHERE 1=1";

        // 默认只显示实习盖章分类的公司
        $sxgzCategory = $DB->get_row("SELECT id FROM qingka_wangke_fenlei WHERE name = '实习盖章' LIMIT 1");
        if ($sxgzCategory) {
            $where .= " AND fenlei = '{$sxgzCategory['id']}'";
        }

        // 搜索条件
        if (!empty($search)) {
            $where .= " AND (name LIKE '%{$search}%' OR content LIKE '%{$search}%')";
        }

        // 获取总数
        $total = $DB->get_var("SELECT COUNT(*) FROM qingka_wangke_class {$where}");

        // 获取公司列表
        $companies = $DB->get_results("
            SELECT cid, name, price, content, fenlei, addtime, status
            FROM qingka_wangke_class
            {$where}
            ORDER BY addtime DESC
            LIMIT {$offset}, {$limit}
        ");

        // 转换为数组格式
        $companiesArray = [];
        if ($companies) {
            foreach ($companies as $company) {
                $companyArray = (array)$company;
                $companiesArray[] = $companyArray;
            }
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'companies' => $companiesArray,
                'total' => intval($total),
                'page' => $page,
                'limit' => $limit
            ],
            'debug' => [
                'where_clause' => $where,
                'company_count' => count($companiesArray),
                'raw_companies' => count($companies ?: []),
                'search_params' => [
                    'page' => $page,
                    'limit' => $limit,
                    'search' => $search
                ],
                'sql_query' => "SELECT cid, name, price, content, fenlei, addtime, status FROM qingka_wangke_class {$where} ORDER BY addtime DESC LIMIT {$offset}, {$limit}"
            ]
        ]);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '获取公司列表失败: ' . $e->getMessage()]);
    }
}







/**
 * 删除订单（管理员功能）
 */
function deleteOrder() {
    global $DB, $userrow;

    try {
        // 验证管理员权限
        if (!isset($userrow) || $userrow['uid'] != 1) {
            throw new Exception('权限不足');
        }

        // 获取POST数据
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }

        $orderId = trim(strip_tags(addslashes($input['order_id'] ?? '')));

        if (empty($orderId)) {
            throw new Exception('订单ID不能为空');
        }

        // 验证订单是否存在
        $order = $DB->get_row("SELECT * FROM fd_sxgz_orders WHERE order_id = '{$orderId}'");
        if (!$order) {
            throw new Exception('订单不存在');
        }

        // 删除订单相关的所有文件
        try {
            // 构建文件路径
            $uploadDir = __DIR__ . '/uploads/uid_' . $order['uid'] . '/orderid_' . $orderId . '/';
            $processedDir = __DIR__ . '/processed/uid_' . $order['uid'] . '/orderid_' . $orderId . '/';

            // 删除上传文件目录
            if (is_dir($uploadDir)) {
                deleteDirectory($uploadDir);
            }

            // 删除处理文件目录
            if (is_dir($processedDir)) {
                deleteDirectory($processedDir);
            }

        } catch (Exception $e) {
            // 文件删除失败不影响订单删除，记录日志即可
        }



        // 删除订单记录
        $result = $DB->query("DELETE FROM fd_sxgz_orders WHERE order_id = '{$orderId}'");

        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => '订单删除成功'
            ]);
        } else {
            throw new Exception('删除订单失败');
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * 递归删除目录及其内容
 */
function deleteDirectory($dir) {
    if (!is_dir($dir)) {
        return false;
    }

    $files = array_diff(scandir($dir), array('.', '..'));
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            deleteDirectory($path);
        } else {
            unlink($path);
        }
    }

    return rmdir($dir);
}

/**
 * 更新订单状态
 */
function updateOrderStatus() {
    global $DB, $userrow;

    try {
        // 验证管理员权限
        if (!isset($userrow) || $userrow['uid'] != 1) {
            throw new Exception('权限不足');
        }

        // 获取POST数据
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }

        $orderId = trim(strip_tags(addslashes($input['order_id'] ?? '')));
        $status = trim(strip_tags(addslashes($input['status'] ?? '')));
        $notes = trim(strip_tags(addslashes($input['notes'] ?? '')));

        if (empty($orderId)) {
            throw new Exception('订单ID不能为空');
        }

        if (empty($status)) {
            throw new Exception('状态不能为空');
        }

        // 验证状态值
        $validStatuses = ['pending', 'processing', 'completed', 'failed', 'refund_requested', 'refunded'];
        if (!in_array($status, $validStatuses)) {
            throw new Exception('无效的状态值');
        }

        // 验证订单是否存在
        $order = $DB->get_row("SELECT * FROM fd_sxgz_orders WHERE order_id = '{$orderId}'");
        if (!$order) {
            throw new Exception('订单不存在');
        }

        // 构建更新SQL - 不再更新备注
        $updateFields = ["status = '{$status}'", "updated_at = NOW()"];

        // 如果状态是完成，设置完成时间
        if ($status === 'completed') {
            $updateFields[] = "completed_at = NOW()";
        }

        $updateSql = "UPDATE fd_sxgz_orders SET " . implode(', ', $updateFields) . " WHERE order_id = '{$orderId}'";

        $result = $DB->query($updateSql);

        if ($result) {
            // 使用wlog记录操作日志，而不是写入备注
            $statusText = '';
            switch ($status) {
                case 'processing': $statusText = '开始处理'; break;
                case 'completed': $statusText = '完成'; break;
                case 'failed': $statusText = '标记失败'; break;
                default: $statusText = $status; break;
            }

            // 记录到系统日志
            wlog("订单状态更新", "管理员更新订单 {$orderId} 状态为 {$statusText}" . (!empty($notes) ? "，备注：{$notes}" : ""));

            echo json_encode([
                'success' => true,
                'message' => '订单状态更新成功',
                'order_id' => $orderId,
                'new_status' => $status
            ]);
        } else {
            wlog("订单状态更新失败", "订单 {$orderId} 状态更新失败: " . $DB->error());
            throw new Exception('更新订单状态失败: ' . $DB->error());
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * 处理退款申请
 */
function handleRefund() {
    global $DB, $userrow;

    try {
        // 验证管理员权限
        if (!isset($userrow) || $userrow['uid'] != 1) {
            throw new Exception('权限不足');
        }

        // 获取POST数据
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }

        $orderId = trim(strip_tags(addslashes($input['order_id'] ?? '')));
        $action = trim(strip_tags(addslashes($input['action'] ?? '')));
        $notes = trim(strip_tags(addslashes($input['notes'] ?? '')));

        if (empty($orderId)) {
            throw new Exception('订单ID不能为空');
        }

        if (empty($action) || !in_array($action, ['approve', 'reject'])) {
            throw new Exception('无效的操作类型');
        }

        // 验证订单是否存在且状态为申请退款
        $order = $DB->get_row("SELECT * FROM fd_sxgz_orders WHERE order_id = '{$orderId}' AND status = 'refund_requested'");
        if (!$order) {
            throw new Exception('订单不存在或状态不正确');
        }

        // 更新订单状态 - 不再更新备注
        $newStatus = $action === 'approve' ? 'refunded' : 'completed';
        $actionText = $action === 'approve' ? '批准退款' : '拒绝退款';

        $updateSql = "UPDATE fd_sxgz_orders SET
                      status = '{$newStatus}',
                      updated_at = NOW()
                      WHERE order_id = '{$orderId}'";

        $result = $DB->query($updateSql);

        if ($result) {
            // 使用wlog记录操作日志，而不是写入备注
            $logMessage = "管理员{$actionText}订单 {$orderId}";
            if (!empty($notes)) {
                $logMessage .= "，备注：{$notes}";
            }
            wlog("退款处理", $logMessage);

            echo json_encode([
                'success' => true,
                'message' => $actionText . '成功',
                'order_id' => $orderId,
                'action' => $action
            ]);
        } else {
            wlog("退款处理失败", "订单 {$orderId} {$actionText}失败: " . $DB->error());
            throw new Exception($actionText . '失败: ' . $DB->error());
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}



/**
 * 刷新公司列表（强制从上游获取）
 */
function refreshCompanies() {
    validateUser();

    try {
        $companies = fetchCompaniesFromUpstream();

        // 更新缓存
        $cacheFile = getCacheFilePath('companies_cache.json');
        $cacheData = [
            'timestamp' => time(),
            'companies' => $companies
        ];
        file_put_contents($cacheFile, json_encode($cacheData, JSON_UNESCAPED_UNICODE));

        echo json_encode([
            'success' => true,
            'data' => $companies,
            'message' => '公司列表已刷新',
            'cache_time' => date('Y-m-d H:i:s')
        ]);

    } catch (Exception $e) {
        throw new Exception('刷新公司列表失败：' . $e->getMessage(), 500);
    }
}

/**
 * 从上游获取公司列表
 */
function fetchCompaniesFromUpstream() {


    $upstreamConfigFile = getUpstreamConfigPath();

    if (!$upstreamConfigFile) {
        throw new Exception('未配置上游，无法获取公司列表');
    }


    $config = include($upstreamConfigFile);
    if (empty($config['upstream_url']) || empty($config['upstream_uid']) || empty($config['upstream_key'])) {
        throw new Exception('上游配置不完整');
    }

    // 构建请求URL
    $baseUrl = rtrim($config['upstream_url'], '/');
    $url = $baseUrl . '/sxgz/api.php?action=get_companies_for_agent&uid=' . $config['upstream_uid'] . '&key=' . $config['upstream_key'];


    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'User-Agent: SXGZ-Plugin'
    ]);

    $startTime = microtime(true);
    $response = curl_exec($ch);
    $endTime = microtime(true);

    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception('网络请求失败: ' . $error);
    }

    if ($httpCode !== 200) {
        throw new Exception('上游服务器连接失败，HTTP状态码：' . $httpCode);
    }

    $result = json_decode($response, true);
    if (!$result) {
        throw new Exception('响应格式错误');
    }

    if (!$result['success']) {
        throw new Exception('上游返回错误：' . ($result['message'] ?? '未知错误'));
    }

    return $result['data'];
}

/**
 * 文件下载接口 - 合并自 download.php
 * 支持权限验证和安全下载
 */
function downloadFile() {
    global $DB, $userrow;

    // 获取参数
    $uid = $_GET['uid'] ?? '';
    $orderId = $_GET['order_id'] ?? '';
    $fileName = $_GET['file'] ?? '';
    $token = $_GET['token'] ?? '';

    // 参数验证
    if (empty($uid) || empty($orderId) || empty($fileName)) {
        http_response_code(400);
        die('缺少必要参数');
    }

    // 验证文件路径安全性
    if (strpos($fileName, '..') !== false || strpos($fileName, '/') !== false || strpos($fileName, '\\') !== false) {
        http_response_code(403);
        die('非法文件路径');
    }

    // 构建文件路径
    $filePath = __DIR__ . '/uploads/uid_' . $uid . '/orderid_' . $orderId . '/' . $fileName;

    // 检查文件是否存在
    if (!file_exists($filePath)) {
        http_response_code(404);
        die('文件不存在');
    }

    // 权限验证
    $hasPermission = false;

    // 1. 检查是否是订单所有者
    if (isset($userrow) && $userrow['uid'] == $uid) {
        $hasPermission = true;
    }

    // 2. 检查是否是管理员
    if (isset($userrow) && $userrow['uid'] == 1) {
        $hasPermission = true;
    }

    // 3. 检查是否是来自上游的请求（通过token验证）
    if (!empty($token)) {
        // 验证token是否有效
        $upstreamConfigFile = __DIR__ . '/upstream.php';
        if (file_exists($upstreamConfigFile)) {
            $config = include($upstreamConfigFile);
            if (!empty($config['upstream_key'])) {
                // 生成预期的token
                $expectedToken = md5($orderId . $config['upstream_key'] . date('Y-m-d'));
                if ($token === $expectedToken) {
                    $hasPermission = true;
                }
            }
        }
    }

    // 4. 检查数据库中的订单权限
    if (!$hasPermission && isset($DB)) {
        $order = $DB->get_row("SELECT * FROM fd_sxgz_orders WHERE order_id = '{$orderId}' AND uid = '{$uid}'");
        if ($order) {
            $hasPermission = true;
        }
    }

    // 如果没有权限，返回403
    if (!$hasPermission) {
        http_response_code(403);
        die('无权限访问此文件');
    }

    // 获取文件信息
    $fileSize = filesize($filePath);
    $fileName = basename($filePath);

    // 获取原始文件名（从数据库）
    $originalName = $fileName;
    if (isset($DB)) {
        $order = $DB->get_row("SELECT original_filename FROM fd_sxgz_orders WHERE order_id = '{$orderId}'");
        if ($order && !empty($order['original_filename'])) {
            $originalName = $order['original_filename'];
        }
    }

    // 设置下载头
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $originalName . '"');
    header('Content-Length: ' . $fileSize);
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');

    // 输出文件内容
    readfile($filePath);
    exit;
}
?>