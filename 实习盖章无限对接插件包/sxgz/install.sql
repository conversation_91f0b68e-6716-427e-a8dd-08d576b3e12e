-- 实习盖章无限对接插件包数据库安装脚本
-- 执行前请确保已备份数据库
--
-- 版本更新说明：
-- 1. 添加退款相关状态：refund_requested, refunded
-- 2. 添加退款原因字段：refund_reason
-- 3. 添加文件URL传递功能：processed_file_url
-- 4. 移除邮件队列功能（邮件由源台统一发送）
-- 5. 精简数据库结构，只保留核心订单表
-- 6. 移除公司和分类数据（插件包通过API从上游获取）

-- 检查表是否存在，如果存在则跳过创建
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables
                     WHERE table_schema = DATABASE() AND table_name = 'fd_sxgz_orders');

-- 创建实习盖章订单表
SET @sql = IF(@table_exists = 0,
'CREATE TABLE `fd_sxgz_orders` (
  `order_id` int(11) NOT NULL AUTO_INCREMENT COMMENT \'订单ID\',
  `uid` int(11) NOT NULL COMMENT \'用户ID\',
  `order_no` varchar(32) NOT NULL COMMENT \'订单号\',

  -- 服务信息
  `service_type` enum(\'electronic\',\'mail\',\'both\') NOT NULL COMMENT \'服务类型：electronic=电子版，mail=邮寄，both=邮寄+电子版\',
  `company_id` int(11) NOT NULL COMMENT \'公司ID（来自上游API）\',
  `company_name` varchar(255) NOT NULL COMMENT \'公司名称\',
  `business_license` tinyint(1) DEFAULT 0 COMMENT \'是否需要营业执照\',
  `only_business_license` tinyint(1) DEFAULT 0 COMMENT \'是否仅需要营业执照\',

  -- 材料信息
  `material_type` enum(\'upload\',\'mail\') DEFAULT NULL COMMENT \'材料方式：upload=在线上传，mail=邮寄纸质\',
  `uploaded_file` varchar(500) DEFAULT NULL COMMENT \'上传文件路径\',
  `original_filename` varchar(255) DEFAULT NULL COMMENT \'原始文件名\',
  `file_size` int(11) DEFAULT NULL COMMENT \'文件大小（字节）\',

  -- 客户信息
  `customer_name` varchar(100) NOT NULL COMMENT \'客户姓名\',
  `customer_email` varchar(255) DEFAULT NULL COMMENT \'客户邮箱\',
  `customer_phone` varchar(20) DEFAULT NULL COMMENT \'客户手机号\',
  `customer_address` text DEFAULT NULL COMMENT \'收货地址\',

  -- 快递信息（邮寄纸质材料时使用）
  `courier_company` varchar(50) DEFAULT NULL COMMENT \'快递公司\',
  `tracking_number` varchar(100) DEFAULT NULL COMMENT \'快递单号\',

  -- 打印选项
  `print_copies` int(11) DEFAULT 0 COMMENT \'打印章数\',
  `print_options` text DEFAULT NULL COMMENT \'打印选项（JSON格式）\',

  -- 价格信息
  `base_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT \'基础费用\',
  `mail_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT \'邮寄费用\',
  `print_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT \'打印费用\',
  `license_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT \'营业执照费用\',
  `total_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT \'总价格\',

  -- 订单状态
  `status` enum(\'pending\',\'processing\',\'completed\',\'cancelled\',\'failed\',\'refund_requested\',\'refunded\') NOT NULL DEFAULT \'pending\' COMMENT \'订单状态\',
  `admin_notes` text DEFAULT NULL COMMENT \'管理员备注\',
  `refund_reason` text DEFAULT NULL COMMENT \'退款原因\',
  `processed_files` text DEFAULT NULL COMMENT \'已处理文件路径（JSON格式）\',
  `processed_file_url` varchar(500) DEFAULT NULL COMMENT \'处理后文件URL（来自上游源台）\',

  -- 代理商相关字段
  `source` enum(\'direct\',\'agent\') NOT NULL DEFAULT \'direct\' COMMENT \'订单来源：direct=直接下单，agent=代理商订单\',
  `agent_uid` int(11) DEFAULT NULL COMMENT \'代理商用户ID（如果是代理商订单）\',
  `agent_order_id` int(11) DEFAULT NULL COMMENT \'代理商端的订单ID\',

  -- 时间信息
  `created_at` datetime NOT NULL COMMENT \'创建时间\',
  `updated_at` datetime DEFAULT NULL COMMENT \'更新时间\',
  `completed_at` datetime DEFAULT NULL COMMENT \'完成时间\',

  PRIMARY KEY (`order_id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `uid` (`uid`),
  KEY `company_id` (`company_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`),
  KEY `source` (`source`),
  KEY `agent_uid` (`agent_uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT=\'实习盖章订单表\'',
'SELECT \'Table fd_sxgz_orders already exists\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示安装结果
SELECT
    'fd_sxgz_orders' as table_name,
    CASE WHEN COUNT(*) > 0 THEN 'EXISTS' ELSE 'NOT FOUND' END as status
FROM information_schema.tables
WHERE table_schema = DATABASE() AND table_name = 'fd_sxgz_orders';

-- 性能优化：添加索引（使用兼容语法）
-- 为fd_sxgz_orders表添加索引
SET @sql = 'ALTER TABLE fd_sxgz_orders ADD INDEX idx_uid_status (uid, status)';
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_schema = DATABASE() AND table_name = 'fd_sxgz_orders' AND index_name = 'idx_uid_status') = 0, @sql, 'SELECT "Index idx_uid_status already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE fd_sxgz_orders ADD INDEX idx_status_created (status, created_at)';
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_schema = DATABASE() AND table_name = 'fd_sxgz_orders' AND index_name = 'idx_status_created') = 0, @sql, 'SELECT "Index idx_status_created already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = 'ALTER TABLE fd_sxgz_orders ADD INDEX idx_order_no (order_no)';
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_schema = DATABASE() AND table_name = 'fd_sxgz_orders' AND index_name = 'idx_order_no') = 0, @sql, 'SELECT "Index idx_order_no already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- 分析表以更新统计信息
ANALYZE TABLE fd_sxgz_orders;

-- 现有数据库升级脚本（如果表已存在）
-- 检查是否需要升级status字段
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                      WHERE table_schema = DATABASE()
                      AND table_name = 'fd_sxgz_orders'
                      AND column_name = 'status'
                      AND column_type LIKE '%refund_requested%');

-- 如果status字段不包含退款状态，则升级
SET @sql = IF(@column_exists = 0,
'ALTER TABLE fd_sxgz_orders
MODIFY COLUMN `status` enum(\'pending\',\'processing\',\'completed\',\'cancelled\',\'failed\',\'refund_requested\',\'refunded\') NOT NULL DEFAULT \'pending\' COMMENT \'订单状态\'',
'SELECT \'Status column already upgraded\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查是否需要添加refund_reason字段
SET @refund_reason_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                             WHERE table_schema = DATABASE()
                             AND table_name = 'fd_sxgz_orders'
                             AND column_name = 'refund_reason');

-- 如果refund_reason字段不存在，则添加
SET @sql = IF(@refund_reason_exists = 0,
'ALTER TABLE fd_sxgz_orders
ADD COLUMN `refund_reason` text DEFAULT NULL COMMENT \'退款原因\' AFTER `admin_notes`',
'SELECT \'Refund reason column already exists\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查是否需要添加processed_file_url字段
SET @processed_file_url_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                                  WHERE table_schema = DATABASE()
                                  AND table_name = 'fd_sxgz_orders'
                                  AND column_name = 'processed_file_url');

-- 如果processed_file_url字段不存在，则添加
SET @sql = IF(@processed_file_url_exists = 0,
'ALTER TABLE fd_sxgz_orders
ADD COLUMN `processed_file_url` varchar(500) DEFAULT NULL COMMENT \'处理后文件URL（来自上游源台）\' AFTER `processed_files`',
'SELECT \'Processed file URL column already exists\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查是否需要添加代理商相关字段
SET @source_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                      WHERE table_schema = DATABASE()
                      AND table_name = 'fd_sxgz_orders'
                      AND column_name = 'source');

-- 如果source字段不存在，则添加
SET @sql = IF(@source_exists = 0,
'ALTER TABLE fd_sxgz_orders
ADD COLUMN `source` enum(\'direct\',\'agent\') NOT NULL DEFAULT \'direct\' COMMENT \'订单来源：direct=直接下单，agent=代理商订单\' AFTER `processed_file_url`,
ADD COLUMN `agent_uid` int(11) DEFAULT NULL COMMENT \'代理商用户ID（如果是代理商订单）\' AFTER `source`,
ADD COLUMN `agent_order_id` int(11) DEFAULT NULL COMMENT \'代理商端的订单ID\' AFTER `agent_uid`,
ADD INDEX `source` (`source`),
ADD INDEX `agent_uid` (`agent_uid`)',
'SELECT \'Agent fields already exist\' as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 安装完成提示
SELECT '实习盖章无限对接插件包数据库安装/升级完成！' as message,
       '✅ 核心订单表已创建' as orders_table,
       '✅ 索引已优化' as indexes,
       '✅ 代理商功能已启用' as agent_support,
       '✅ 文件URL传递功能已启用' as file_url_support,
       '📋 公司列表通过API从上游获取' as company_source;
