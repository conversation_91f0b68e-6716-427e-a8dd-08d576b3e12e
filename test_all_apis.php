<?php
/**
 * 三层对接功能完整测试脚本
 * 
 * 测试架构：
 * 插件2 (36355) → 插件1 (3652) → 源台 (8980)
 */

echo "=== 实习盖章三层对接功能测试 ===\n\n";

// 测试配置
$config = [
    'source' => 'http://localhost:8980',      // 源台
    'plugin1' => 'http://localhost:3652',     // 插件1
    'plugin2' => 'http://localhost:36355',    // 插件2
    'test_uid' => 1,
    'test_key' => '22T94Q9cH9ZExB12'
];

/**
 * 执行HTTP请求
 */
function makeRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    if ($method === 'POST' && $data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => $httpCode === 200 && !$error,
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

/**
 * 测试结果输出
 */
function testResult($testName, $result, $expectSuccess = true) {
    $status = $result['success'] ? '✅' : '❌';
    echo "{$status} {$testName}\n";
    
    if (!$result['success']) {
        echo "   错误: HTTP {$result['http_code']} - {$result['error']}\n";
        if ($result['response']) {
            echo "   响应: " . substr($result['response'], 0, 200) . "\n";
        }
    } else {
        $data = json_decode($result['response'], true);
        if ($data && isset($data['success'])) {
            if ($data['success'] === $expectSuccess) {
                echo "   状态: 成功\n";
                if (isset($data['message'])) {
                    echo "   消息: {$data['message']}\n";
                }
            } else {
                echo "   状态: 失败 - {$data['message']}\n";
            }
        }
    }
    echo "\n";
    return $result['success'];
}

// 开始测试
echo "1. 测试基础连通性\n";
echo "==================\n";

// 测试源台
$result = makeRequest($config['source'] . '/sxgz/api.php?action=get_companies_for_agent&uid=' . $config['test_uid'] . '&key=' . $config['test_key']);
testResult("源台API连通性", $result);

// 测试插件1
$result = makeRequest($config['plugin1'] . '/apitaowa.php?action=get_companies_for_agent&uid=' . $config['test_uid'] . '&key=' . $config['test_key']);
testResult("插件1代理商API连通性", $result);

// 测试插件2
$result = makeRequest($config['plugin2'] . '/apitaowa.php?action=get_companies_for_agent&uid=' . $config['test_uid'] . '&key=' . $config['test_key']);
testResult("插件2代理商API连通性", $result);

echo "\n2. 测试公司列表获取\n";
echo "==================\n";

// 测试各层级获取公司列表
$endpoints = [
    '源台' => $config['source'] . '/sxgz/api.php?action=get_companies_for_agent&uid=' . $config['test_uid'] . '&key=' . $config['test_key'],
    '插件1' => $config['plugin1'] . '/apitaowa.php?action=get_companies_for_agent&uid=' . $config['test_uid'] . '&key=' . $config['test_key'],
    '插件2' => $config['plugin2'] . '/apitaowa.php?action=get_companies_for_agent&uid=' . $config['test_uid'] . '&key=' . $config['test_key']
];

foreach ($endpoints as $name => $url) {
    $result = makeRequest($url);
    if (testResult("{$name}获取公司列表", $result)) {
        $data = json_decode($result['response'], true);
        if ($data && isset($data['data'])) {
            echo "   公司数量: " . count($data['data']) . "\n";
            if (!empty($data['data'])) {
                $company = $data['data'][0];
                echo "   示例公司: {$company['name']} - ¥{$company['price']}\n";
            }
        }
    }
    echo "\n";
}

echo "\n3. 测试订单创建链路\n";
echo "==================\n";

// 准备测试订单数据
$orderData = [
    'service_type' => 'electronic',
    'company_id' => 2881,  // 使用测试公司ID
    'customer_name' => '测试用户',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '***********',
    'customer_address' => '测试地址',
    'special_requirements' => '三层对接测试订单',
    'print_copies' => 5,
    'business_license' => false
];

// 测试插件2创建订单（应该转发到插件1，再转发到源台）
$result = makeRequest($config['plugin2'] . '/apitaowa.php?action=create_order&uid=' . $config['test_uid'] . '&key=' . $config['test_key'], 'POST', $orderData);
testResult("插件2创建订单（三层转发）", $result);

if ($result['success']) {
    $data = json_decode($result['response'], true);
    if ($data && isset($data['data']['order_id'])) {
        $testOrderId = $data['data']['order_id'];
        echo "   创建的订单ID: {$testOrderId}\n";
        
        // 测试订单查询
        echo "\n4. 测试订单查询\n";
        echo "================\n";
        
        // 从各层级查询订单
        $queryEndpoints = [
            '源台' => $config['source'] . '/sxgz/api.php?action=get_order&order_id=' . $testOrderId,
            '插件1' => $config['plugin1'] . '/apitaowa.php?action=get_order&order_id=' . $testOrderId . '&uid=' . $config['test_uid'] . '&key=' . $config['test_key'],
            '插件2' => $config['plugin2'] . '/apitaowa.php?action=get_order&order_id=' . $testOrderId . '&uid=' . $config['test_uid'] . '&key=' . $config['test_key']
        ];
        
        foreach ($queryEndpoints as $name => $url) {
            $result = makeRequest($url);
            testResult("{$name}查询订单", $result);
        }
    }
}

echo "\n5. 测试订单列表同步\n";
echo "==================\n";

// 测试订单列表同步
$syncEndpoints = [
    '插件1' => $config['plugin1'] . '/apitaowa.php?action=sync_orders&uid=' . $config['test_uid'] . '&key=' . $config['test_key'],
    '插件2' => $config['plugin2'] . '/apitaowa.php?action=sync_orders&uid=' . $config['test_uid'] . '&key=' . $config['test_key']
];

foreach ($syncEndpoints as $name => $url) {
    $result = makeRequest($url);
    if (testResult("{$name}同步订单列表", $result)) {
        $data = json_decode($result['response'], true);
        if ($data && isset($data['data'])) {
            echo "   订单数量: " . count($data['data']) . "\n";
        }
    }
    echo "\n";
}

echo "\n6. 测试统计数据获取\n";
echo "==================\n";

// 测试统计数据
$statsEndpoints = [
    '插件1' => $config['plugin1'] . '/apitaowa.php?action=get_statistics&uid=' . $config['test_uid'] . '&key=' . $config['test_key'],
    '插件2' => $config['plugin2'] . '/apitaowa.php?action=get_statistics&uid=' . $config['test_uid'] . '&key=' . $config['test_key']
];

foreach ($statsEndpoints as $name => $url) {
    $result = makeRequest($url);
    if (testResult("{$name}获取统计数据", $result)) {
        $data = json_decode($result['response'], true);
        if ($data && isset($data['data']['overview'])) {
            $overview = $data['data']['overview'];
            echo "   总订单: {$overview['total_orders']}\n";
            echo "   待处理: {$overview['pending_orders']}\n";
            echo "   已完成: {$overview['completed_orders']}\n";
        }
    }
    echo "\n";
}

echo "\n7. 测试缓存刷新\n";
echo "================\n";

// 测试缓存刷新
$refreshEndpoints = [
    '插件1' => $config['plugin1'] . '/apitaowa.php?action=refresh_cache&uid=' . $config['test_uid'] . '&key=' . $config['test_key'],
    '插件2' => $config['plugin2'] . '/apitaowa.php?action=refresh_cache&uid=' . $config['test_uid'] . '&key=' . $config['test_key']
];

foreach ($refreshEndpoints as $name => $url) {
    $result = makeRequest($url, 'POST');
    testResult("{$name}刷新缓存", $result);
}

echo "\n=== 测试完成 ===\n";
echo "如果所有测试都显示 ✅，说明三层对接功能正常工作。\n";
echo "如果有 ❌，请检查对应的错误信息和配置。\n";
?>
